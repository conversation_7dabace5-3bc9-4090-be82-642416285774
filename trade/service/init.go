package service

import (
	"context"

	"github.com/zeromicro/go-zero/core/logx"

	commonConfig "hotel/common/config"
	"hotel/common/cqrs"
	commonService "hotel/common/service"
	"hotel/trade/config"

	"github.com/zeromicro/go-zero/core/conf"
	"github.com/zeromicro/go-zero/core/stores/redis"

	"hotel/supplier"
	"hotel/trade/dao"
)

// TradeService 订单服务结构体
type TradeService struct {
	orderDao         *dao.OrderDao
	supplier         *supplier.Factory
	cache            *redis.Redis
	rebookingService *RebookingService
	sessionService   *commonService.SessionService
	cqrsProducer     cqrs.Producer
	cqrsConfig       *cqrs.Config
	cancelAuditor    *CancelAuditor
	// 移除trackingService，基于BIOrder记录关键指标
}

var configFile = commonConfig.SafeFlagString("trade", "trade/config/config.yaml", "the order config file")

func NewTradeService() *TradeService {
	c := &config.Config{}
	conf.MustLoad(*configFile, c)

	rcli, err := redis.NewRedis(c.Redis)
	if err != nil {
		panic("redis Init error: " + err.Error())
	}

	// 初始化CQRS消息队列
	cqrsConfig := &cqrs.Config{
		Type: "redis_stream",
		Redis: cqrs.RedisConfig{
			Host:     "127.0.0.1", // 从配置中提取
			Port:     6379,
			Password: "",
			DB:       0,
		},
	}

	// 创建CQRS生产者
	producer, err := cqrs.NewProducer(cqrsConfig)
	if err != nil {
		panic("cqrs producer Init error: " + err.Error())
	}

	os := &TradeService{
		orderDao:       dao.NewOrderDao(c.MySQL.Trade, c.Redis),
		supplier:       supplier.NewFactory(),
		cache:          rcli,
		sessionService: commonService.NewSessionService(rcli),
		cqrsProducer:   producer,
		cqrsConfig:     cqrsConfig,
		// 基于BIOrder的简化追踪，无需独立tracking service
	}

	// 初始化取消操作审计器
	os.cancelAuditor = NewCancelAuditor(os)

	// 初始化重预订服务
	os.rebookingService = NewRebookingService(os)

	if err := os.startOrderAllConsumer(); err != nil {
		//panic("start order consumer failed: " + err.Error())
		logx.Errorf("start order consumer error: %v", err)
	}
	os.startScanOrder()

	// 启动重预订扫描器 (在后台运行)
	go func() {
		ctx := context.Background()
		if err := os.StartRebookingScanner(ctx); err != nil {
			logx.Errorf("start rebooking scanner error: %v", err)
		}
	}()

	return os
}
func (s *TradeService) Name() string {
	return "trade"
}
