package service

import (
	"context"
	"fmt"
	"time"

	"github.com/spf13/cast"

	"hotel/trade/dao"
	"hotel/trade/domain"
	"hotel/trade/protocol"

	"hotel/common/log"
)

// Cancel
// @desc: Cancel hotel order, including FULL cancel & PARTIAL cancel
// @tags: openapi,booking.hotelbyte.com/trade
// @path: /cancel
func (s *TradeService) Cancel(ctx context.Context, req *protocol.CancelReq) error {
	startTime := time.Now()

	// 参数验证
	if req.OrderId == "" {
		return fmt.Errorf("order ID is required")
	}

	orderId := cast.ToUint64(req.OrderId)
	if orderId == 0 {
		return fmt.Errorf("invalid order ID: %s", req.OrderId)
	}

	// 记录取消请求
	userID := uint64(0) // 这里应该从context中获取用户ID
	s.cancelAuditor.RecordCancelRequest(ctx, orderId, userID, "User requested cancellation")

	// 查询订单
	order, err := s.orderDao.Order.FindOne(ctx, orderId)
	if err != nil {
		log.Errorc(ctx, "Failed to find order %d: %v", orderId, err)
		return fmt.Errorf("order not found: %w", err)
	}

	// 创建状态机进行状态验证
	currentStatus := domain.OrderStatus(order.Status)
	sm, err := domain.NewOrderStateMachine(currentStatus)
	if err != nil {
		log.Errorc(ctx, "Failed to create state machine for order %d: %v", orderId, err)
		return fmt.Errorf("invalid order state: %w", err)
	}

	// 检查是否可以转换到NeedCancel状态
	if !sm.CanTransitionTo(domain.OrderStateNeedCancel) {
		log.Warnc(ctx, "Order %d cannot be cancelled in current state: %s", orderId, currentStatus.String())
		return fmt.Errorf("order cannot be cancelled in current state: %s", currentStatus.String())
	}

	// 执行状态转换
	if err := sm.TransitionToWithReason(domain.OrderStateNeedCancel, "User requested cancellation"); err != nil {
		log.Errorc(ctx, "Failed to transition order %d state: %v", orderId, err)
		return fmt.Errorf("failed to update order state: %w", err)
	}

	// 更新订单状态到数据库
	order.Status = domain.OrderStateNeedCancel.Int64()
	if err = s.orderDao.Order.Update(ctx, order); err != nil {
		log.Errorc(ctx, "CancelOrderFailed: order=%+v, UpdateError=%+v", order, err)
		return fmt.Errorf("failed to update order status: %w", err)
	}

	// 发布取消消息到CQRS队列
	if sendErr := s.cqrsProducer.Publish(ctx, CancelTopic, order); sendErr != nil {
		log.Errorc(ctx, "CancelOrderFailed: order=%+v, PubOrderError=%+v", order, sendErr)
		// 消息发布失败，需要回滚订单状态
		if rollbackErr := s.rollbackOrderStatus(ctx, order, currentStatus); rollbackErr != nil {
			log.Errorc(ctx, "Failed to rollback order %d status: %v", orderId, rollbackErr)
		}

		// 记录失败审计
		processingTime := time.Since(startTime).Milliseconds()
		s.cancelAuditor.RecordCancelFailure(ctx, orderId, currentStatus, "message_queue", sendErr.Error(), processingTime)

		return fmt.Errorf("failed to publish cancel message: %w", sendErr)
	}

	// 记录成功审计
	processingTime := time.Since(startTime).Milliseconds()
	s.cancelAuditor.RecordCancelSuccess(ctx, order, currentStatus, true, processingTime)

	log.Infoc(ctx, "Order %d cancel request processed successfully", orderId)
	return nil
}

// rollbackOrderStatus 回滚订单状态
func (s *TradeService) rollbackOrderStatus(ctx context.Context, order *dao.Order, originalStatus domain.OrderStatus) error {
	order.Status = originalStatus.Int64()
	if err := s.orderDao.Order.Update(ctx, order); err != nil {
		return fmt.Errorf("failed to rollback order status: %w", err)
	}
	log.Infoc(ctx, "Order %d status rolled back to %s", order.Id, originalStatus.String())
	return nil
}
