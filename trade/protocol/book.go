package protocol

import (
	supplierDomain "hotel/supplier/domain"
	userDomain "hotel/user/domain"
)

type BookReq struct {
	supplierDomain.BookReq
	ReferenceNo string                 `json:"referenceNo,omitempty"`
	Operator    *userDomain.User       `json:"operator" apidoc:"-"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"` // 追踪元数据
}

type BookResp struct {
	supplierDomain.BookResp
	PlatformOrderId int64 `json:"platformOrderId" required:"true"`
}
