package service

import (
	"context"
	"time"

	"hotel/common/httpdispatcher"
	"hotel/common/log"
	"hotel/common/pagehelper"
	"hotel/common/types"
	"hotel/content/domain"
	"hotel/content/mysql"
	"hotel/content/protocol"
	geoDomain "hotel/geography/domain"
	geoSrv "hotel/geography/service"

	"hotel/supplier"
	supplierDomain "hotel/supplier/domain"
	userDomain "hotel/user/domain"
	userProto "hotel/user/protocol"
	userSrv "hotel/user/service"
)

type deps struct {
	dao     *mysql.HotelDao
	factory *supplier.Factory
	userSrv *userSrv.UserService
	geoSrv  *geoSrv.GeographyService
}

type ContentService struct {
	*deps
	importSrv  *ImportService
	opsSrv     *OpsService
	mappingSrv *MappingService
}

func (s *ContentService) GetChildren() []httpdispatcher.Service {
	return []httpdispatcher.Service{s.importSrv, s.opsSrv, s.mappingSrv}
}

func (s *ContentService) Name() string {
	return "content"
}

func (s *ContentService) MGetHotel(ctx context.Context, req *protocol.MGetHotelReq) (*protocol.MGetHotelResp, error) {
	hs, err := s.dao.FindByIDs(ctx, req.HotelIds)
	if err != nil {
		return nil, err
	}
	return &protocol.MGetHotelResp{Hotels: hs}, nil
}

func (s *ContentService) GetHotel(ctx context.Context, req *protocol.GetHotelReq) (*protocol.GetHotelResp, error) {
	hs, err := s.dao.Get(ctx, req.HotelId)
	if err != nil {
		return nil, err
	}
	return &protocol.GetHotelResp{Hotel: hs}, nil
}

func (s *ContentService) ListHotelByRegionIds(ctx context.Context, req *protocol.ListHotelByRegionIdsReq) (*protocol.ListHotelByRegionIdsResp, error) {
	// 使用层级搜索扩展region ID列表
	expandedRegionIds, err := s.geoSrv.GetExpandedRegionIds(ctx, req.RegionIds)
	if err != nil {
		log.Errorc(ctx, "Failed to expand region ids: %v, fallback to original region ids", err)
		// 如果扩展失败，使用原始的region IDs
		expandedRegionIds = req.RegionIds
	}

	hs, pageResp, err := s.dao.ListPage(ctx, expandedRegionIds, req.PageReq, req.InternalSuppliers)
	if err != nil {
		return nil, err
	}
	return &protocol.ListHotelByRegionIdsResp{Hotels: hs, PageResp: pageResp}, nil
}

// FindSimilarHotel
// @tags: internal
func (s *ContentService) FindSimilarHotel(ctx context.Context, hotel *domain.Hotel) ([]*domain.Hotel, error) {
	return s.dao.Find(ctx, hotel)
}

// HotelIdList
// @desc: Get hotel id list by region. Or do hotel id convertion between HotelByte hotel id and your hotel id.
// @tags: openapi,Content,booking.hotelbyte.com/content
// @path: /hotelIdList
// @auth: required
func (s *ContentService) HotelIdList(ctx context.Context, req *protocol.HotelIDListReq) (resp *protocol.HotelIDListResp, err error) {
	var (
		masterHotelIDs []types.ID
		hotels         domain.HotelList
		pageResp       *pagehelper.PageResp
	)
	defer func() {
		if err != nil {
			return
		}
		resp = &protocol.HotelIDListResp{
			Page: pageResp,
		}
		for _, hotelID := range masterHotelIDs {
			hid := protocol.HotelId{
				HotelId: hotelID,
			}
			h := hotels.GetByHotelId(hotelID)
			if h != nil {
				if req.Supplier > 0 {
					hid.HotelSupplierRef = h.HotelSupplierRef.GetBySupplier(req.Supplier)
				}
			}
			resp.HotelIdList = append(resp.HotelIdList, hid)
		}
		entity := req.Operator.GetTenantGroupEntityOrAbove()
		if entity == nil {
			return
		}
		var entityProfiles map[types.ID]*domain.HotelEntityProfile
		entityProfiles, err = s.dao.GetHotelEntityProfiles(ctx, entity.ID, masterHotelIDs)
		if err != nil {
			log.Errorc(ctx, "GetHotelEntityProfiles failed:%+v entityID(%v) masterHotelIDs(%+v)", err, entity.ID, masterHotelIDs)
			return
		}
		for i, v := range resp.HotelIdList {
			v.BusinessHotelId = entityProfiles[v.HotelId].EntityHotelId
			resp.HotelIdList[i] = v // update
		}
	}()

	if regionId := req.RegionId; regionId > 0 {
		hotels, pageResp, err = s.dao.ListPage(ctx, []types.ID{regionId}, req.Page, nil)
		if err != nil {
			return nil, err
		}
		masterHotelIDs = hotels.IDs()
	} else if len(req.HotelIds) > 0 {
		hotels, err = s.dao.FindByIDs(ctx, req.HotelIds)
		if err != nil {
			return nil, err
		}
		masterHotelIDs = hotels.IDs()
	} else if req.Supplier > 0 {
		hotels, pageResp, err = s.dao.ListPage(ctx, nil, req.Page, []supplierDomain.Supplier{req.Supplier})
		if err != nil {
			return nil, err
		}
		masterHotelIDs = hotels.IDs()
	}
	return &protocol.HotelIDListResp{}, nil
}

// FindNearbyHotel
// @tags: internal
func (s *ContentService) FindNearbyHotel(ctx context.Context, req *geoDomain.Nearby) ([]*domain.Hotel, error) {
	return s.dao.FindNearby(ctx, req.Latlng, req.Radius)
}

func (s *ContentService) AddHotel(ctx context.Context, req *protocol.OperateHotelReq) (*protocol.OperateHotelResp, error) {
	if err := s.dao.Insert(ctx, req.Hotel); err != nil {
		return nil, err
	}
	_ = s.userSrv.AddAuditLog(ctx, &userProto.AddAuditLogReq{
		Log: &userDomain.AuditLog{
			ActionType:         "AddHotel",
			ActionTime:         time.Now(),
			ActorUserId:        req.Operator.ID,
			ActorInfo:          req.Operator.BriefString(),
			AffectedEntityId:   0,
			AffectedEntityInfo: "",
			Details: userDomain.AuditLogDetail{
				Raw: req.Hotel,
			},
		},
		Operator: req.Operator,
	})
	return &protocol.OperateHotelResp{}, nil
}
