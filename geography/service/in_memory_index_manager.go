package service

import (
	"fmt"
	"sync/atomic"

	"hotel/common/types"
	"hotel/geography/domain"
	supplierDomain "hotel/supplier/domain"

	"github.com/hashicorp/go-set/v3"
)

type InMemoryIndexManager struct {
	regions         map[types.ID]*domain.Region              // master region id -> Region
	supplierRegions map[string]types.ID                      // supplier region id -> master region id
	typeIndex       map[domain.RegionType]*set.Set[types.ID] // Type -> RegionIDs
	countryIndex    map[string]*set.Set[types.ID]            // CountryCode -> RegionIDs
	descendantIndex map[types.ID]*set.Set[types.ID]          // AncestorID -> DescendantIDs
	loaded          atomic.Bool
}

func NewInMemoryIndexManager() *InMemoryIndexManager {
	return &InMemoryIndexManager{
		regions:         make(map[types.ID]*domain.Region),
		supplierRegions: make(map[string]types.ID),
		typeIndex:       make(map[domain.RegionType]*set.Set[types.ID]),
		countryIndex:    make(map[string]*set.Set[types.ID]),
		descendantIndex: make(map[types.ID]*set.Set[types.ID]),
	}
}

func (m *InMemoryIndexManager) BuildIndexesFromCtripHotelCity(cities []*domain.DeprecatedCtripHotelCity) {
	for _, city := range cities {
		m.mountIndexByRegion(city.ToRegion())
	}
}

func (m *InMemoryIndexManager) mountIndexByRegion(region *domain.Region) {
	if region == nil {
		return
	}
	// 主索引
	m.regions[region.ID] = region
	m.supplierRegions[fmt.Sprintf("%v_%v", supplierDomain.Supplier_Dida, region.DidaId)] = region.ID
	// 类型索引
	if m.typeIndex[region.Type] == nil {
		m.typeIndex[region.Type] = set.New[types.ID](1000)
	}
	m.typeIndex[region.Type].Insert(region.ID)

	// 国家索引
	if m.countryIndex[region.CountryCode] == nil {
		m.countryIndex[region.CountryCode] = set.New[types.ID](100)
	}
	m.countryIndex[region.CountryCode].Insert(region.ID)

	// 后代索引
	for _, ancestor := range region.Ancestors {
		if m.descendantIndex[ancestor.ID] == nil {
			m.descendantIndex[ancestor.ID] = set.New[types.ID](1)
		}
		m.descendantIndex[ancestor.ID].Insert(region.ID)
	}
	// 反向
	for _, descendant := range region.Descendants {
		if m.descendantIndex[region.ID] == nil {
			m.descendantIndex[region.ID] = set.New[types.ID](1)
		}
		m.descendantIndex[region.ID].Insert(descendant.ID)
		m.mountIndexByRegion(descendant)
	}
}

func (m *InMemoryIndexManager) BuildIndexes(regions []*domain.Region) {
	defer func() {
		m.loaded.Store(true)
	}()
	for _, region := range regions {
		if region == nil {
			continue
		}
		// 在构建索引时优化NameFull，去掉冗余信息
		region.OptimizeNameFull()
		m.mountIndexByRegion(region)
	}
}

func (m *InMemoryIndexManager) GetRegionByID(id types.ID) (*domain.Region, bool) {
	region, exists := m.regions[id]
	return region, exists
}

func (m *InMemoryIndexManager) GetRegionBySupplierRegionId(supplier supplierDomain.Supplier, regionId string) (*domain.Region, bool) {
	regionID, exists := m.supplierRegions[fmt.Sprintf("%v_%v", supplier, regionId)]
	return m.regions[regionID], regionID != 0 && exists
}

func (m *InMemoryIndexManager) GetRegionsByType(t domain.RegionType) []*domain.Region {
	var results []*domain.Region
	for id := range m.typeIndex[t].Items() {
		if region, ok := m.regions[id]; ok {
			results = append(results, region)
		}
	}
	return results
}

func (m *InMemoryIndexManager) GetRegionsByCountry(countryCode string) []*domain.Region {
	var results []*domain.Region
	for id := range m.countryIndex[countryCode].Items() {
		if region, ok := m.regions[id]; ok {
			results = append(results, region)
		}
	}
	return results
}

func (m *InMemoryIndexManager) GetAllRegionMap() map[types.ID]*domain.Region {
	return m.regions
}

func (m *InMemoryIndexManager) GetAllRegionList() (out []*domain.Region) {
	for _, v := range m.regions {
		out = append(out, v)
	}
	return out
}
