package domain

import (
	"errors"
	"strings"

	"github.com/paulmach/orb"
	"github.com/paulmach/orb/encoding/wkb"
	pkgerr "github.com/pkg/errors"

	"hotel/common/i18n"
	"hotel/common/types"
)

type SupplierID struct {
	ExpediaId string `json:"expediaId,omitempty"` // expedia geography id
	TripId    string `json:"tripId,omitempty"`    // trip city id
	DidaId    string `json:"didaId,omitempty"`    // dida geography id
}

type Region struct {
	ID       types.ID   `json:"id"`
	Type     RegionType `json:"type,omitempty"`
	Name     string     `json:"name,omitempty"`     // default as NameEn
	NameFull string     `json:"nameFull,omitempty"` // "Springfield, Missouri, United States of America"
	SupplierID
	CountryCode            string      `json:"countryCode,omitempty"`            // specified ISO 3166-1 alpha-2 country code, see https://www.iso.org/obp/ui/#search
	CountrySubdivisionCode string      `json:"countrySubdivisionCode,omitempty"` // ISO 3166-2 country subdivision
	Coordinates            Coordinates `json:"coordinates,omitzero"`             // Location desc
	Ancestors              []*Region   `json:"ancestors,omitzero"`               // An array of the region's ancestors. Sorted from root.
	Descendants            []*Region   `json:"descendants,omitzero"`             // Optional. An array of the region's descendants.
	Extra                  Extra       `json:"extra,omitzero"`
}

// OptimizeNameFull 优化region的NameFull字段，去掉冗余信息
// 例如："上海，上海，中国" -> "上海，中国"
func (r *Region) OptimizeNameFull() {
	if r.NameFull == "" {
		return
	}

	// 按逗号分割NameFull
	parts := strings.Split(r.NameFull, ",")
	if len(parts) <= 1 {
		return
	}

	// 去掉空格并去重
	var cleanParts []string
	seen := make(map[string]bool)

	for _, part := range parts {
		trimmed := strings.TrimSpace(part)
		if trimmed != "" && !seen[trimmed] {
			cleanParts = append(cleanParts, trimmed)
			seen[trimmed] = true
		}
	}

	// 重新组合NameFull
	if len(cleanParts) > 0 {
		r.NameFull = strings.Join(cleanParts, ", ")
	}
}

func (r *Region) ToRegionIndexDoc() *RegionIndexDoc {
	return &RegionIndexDoc{
		ID: r.ID.Int64(),
		Name: NameIndex{
			Ngram:       r.Name,
			PrefixNgram: r.Name,
			SuffixNgram: r.Name,
			Keyword:     r.Name,
		},
		NameZh: NameZhIndex{
			Pinyin:  r.Extra.NameZh,
			Keyword: r.Extra.NameZh,
			Jieba:   r.Extra.NameZh,
		},
	}
}

type RegionIndexDoc struct {
	ID     int64       `json:"id"`
	Name   NameIndex   `json:"name"`
	NameZh NameZhIndex `json:"nameZh"`
}
type NameIndex struct {
	Ngram       string `json:"ngram,omitempty"`
	PrefixNgram string `json:"prefix_ngram,omitempty"`
	SuffixNgram string `json:"suffix_ngram,omitempty"`
	Keyword     string `json:"keyword,omitempty"`
}
type NameZhIndex struct {
	Pinyin  string `json:"pinyin,omitempty"`
	Keyword string `json:"keyword,omitempty"`
	Jieba   string `json:"jieba,omitempty"`
}
type RegionSummary struct {
	ID   int64     `json:"id,omitempty"`
	Name i18n.I18N `json:"name,omitzero"`
}

func (s RegionSummary) MergeUpdate(in RegionSummary) RegionSummary {
	if in.ID != s.ID {
		s.ID = in.ID
	}
	s.Name = s.Name.MergeUpdate(in.Name)
	return s
}

type CitySummary struct {
	City    RegionSummary `json:"city"`
	Country RegionSummary `json:"country"`
}

func NewCitySummary(city, country RegionSummary) CitySummary {
	return CitySummary{
		City:    city,
		Country: country,
	}
}

func (c CitySummary) MergeUpdate(in CitySummary) CitySummary {
	c.City = in.City.MergeUpdate(c.City)
	c.Country = in.Country.MergeUpdate(c.Country)
	return c
}

type Extra struct {
	NameZh          string `json:"nameZh,omitempty"`        // Region Name in Chinese
	FullNameZh      string `json:"fullNameZh,omitempty"`    // Full Name in Chinese
	CountryName     string `json:"countryName,omitempty"`   // ; China (Includes HK,MC,TW)
	CountryNameZh   string `json:"countryNameZh,omitempty"` // ; China (Includes HK,MC,TW)
	CityName        string `json:"cityName,omitempty"`      // City Name (if region is county, under city)
	ProvinceName    string `json:"provinceName,omitempty"`  // Province Name
	ContinentNameZh string `json:"continentName,omitempty"` //
}

type Coordinates struct {
	CenterLat       float64         `json:"centerLat,omitempty"`
	CenterLng       float64         `json:"centerLng,omitempty"`
	BoundingPolygon BoundingPolygon `json:"boundingPolygon,omitzero"`
}

type BoundingPolygon struct {
	Type        string   `json:"type,omitempty"` // Polygon,
	Coordinates []Latlng `json:"coordinates,omitzero"`
}

// 使用 orb 库的 Point 类型
type GeoPoint struct {
	Point orb.Point
	SRID  uint32 `json:"srid"`
}

// NewGeoPoint （经度在前，纬度在后）
func NewGeoPoint(lat, lng float64, srid uint32) GeoPoint {
	return GeoPoint{
		Point: orb.Point{lng, lat}, // 注意经纬度顺序
		SRID:  srid,
	}
}

func NewWGS84GeoPoint(lat, lng float64) GeoPoint {
	return NewGeoPoint(lat, lng, SRID_WGS84)
}
func ConvertLatLng2GeoPoint(in *Latlng) GeoPoint {
	if in == nil {
		return GeoPoint{}
	}
	return NewGeoPoint(in.Lat, in.Lng, SRID_WGS84)
}
func ConvertGeoPoint2LatLng(in GeoPoint) Latlng {
	return Latlng{
		Lng: in.Point.Lon(),
		Lat: in.Point.Lat(),
	}
}

func ConvertGeoPoint2LatLngPointer(in GeoPoint) *Latlng {
	if in.SRID == 0 {
		return nil
	}
	return &Latlng{
		Lng: in.Point.Lon(),
		Lat: in.Point.Lat(),
	}
}

const (
	SRID_WGS84 = 4326
)

// 实现自定义 Scan 方法
func (p *GeoPoint) Scan(value interface{}) error {
	data, ok := value.([]byte)
	if !ok {
		return errors.New("invalid geometry type")
	}

	// 处理带 SRID 的 WKB 格式
	if len(data) < 4 {
		return errors.New("invalid wkb data")
	}

	// 解析 SRID（MySQL 使用 4 字节小端序）
	p.SRID = uint32(data[0]) | uint32(data[1])<<8 |
		uint32(data[2])<<16 | uint32(data[3])<<24

	// 跳过 SRID 头（4 字节）
	geom, err := wkb.Unmarshal(data[4:])
	if err != nil {
		return err
	}

	if point, ok := geom.(orb.Point); ok {
		p.Point = point
		return nil
	}
	return errors.New("not a point")
}

type Latlng struct {
	Lat float64 `json:"lat"`
	Lng float64 `json:"lng"`
}

// 实现 sql.Scanner 接口
func (p *Latlng) Scan(value interface{}) error {
	// 验证输入类型
	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("invalid geometry type")
	}

	if len(bytes) == 0 {
		return nil
	}
	// 解码 WKB
	geom, err := wkb.Unmarshal(bytes)
	if err != nil {
		return pkgerr.Wrapf(err, "in(%s)", string(bytes))
	}

	// 转换为 Point 类型
	if g, ok := geom.(orb.Point); ok {
		p.Lng = g.X()
		p.Lat = g.Y()
		return nil
	}

	// 验证有效经纬度范围
	if p.Lng < -180 || p.Lng > 180 || p.Lat < -90 || p.Lat > 90 {
		return errors.New("invalid coordinate range")
	}
	return errors.New("not a point geometry")
}

type LatlngCoordinator struct {
	Google *Latlng `json:"google,omitempty" required:"true"`
	Gaode  *Latlng `json:"gaode,omitempty"`
}

func (p LatlngCoordinator) IsEmpty() bool {
	v, _ := p.Extract()
	return v == nil
}

func (p LatlngCoordinator) MergeUpdate(c LatlngCoordinator) LatlngCoordinator {
	if c.Google != nil {
		p.Google = c.Google
	}
	if c.Gaode != nil {
		p.Gaode = c.Gaode
	}
	return p
}
func (p LatlngCoordinator) Extract() (*Latlng, string) {
	if p.Google != nil {
		return p.Google, GeoSourceGoogle
	}
	if p.Gaode != nil {
		return p.Gaode, GeoSourceGaode
	}
	return nil, ""
}

type GeoSource = string

const (
	GeoSourceGoogle GeoSource = "google"
	GeoSourceGaode  GeoSource = "gaode"
)
