package mysql

import (
	"context"

	"github.com/zeromicro/go-zero/core/stores/sqlx"
)

type Dao struct {
	conn               sqlx.SqlConn
	User               *UserDao
	UserOptimized      *OptimizedUserDao
	Role               *RoleDao
	Entity             *EntityDao
	Otp                *OtpDao
	AuditLog           *AuditLogDao
	SupplierCredential *SupplierCredentialDao
}

func NewDao(conn sqlx.SqlConn) *Dao {
	return &Dao{
		conn: conn,
		User: newUserDao(NewUserModel(conn), NewUserRoleModel(conn), NewRoleModel(conn), NewEntityModel(conn),
			NewEntityUserLinkModel(conn), NewEntityEntityLinkModel(conn)),
		UserOptimized:      &OptimizedUserDao{conn: conn},
		Role:               newRoleDao(NewRoleModel(conn), NewUserRoleModel(conn), NewPrivilegeModel(conn)),
		Entity:             newEntityDao(NewEntityModel(conn)),
		Otp:                newOtpDao(NewOtpCodeModel(conn)),
		AuditLog:           newAuditLogDao(NewAuditLogModel(conn)),
		SupplierCredential: NewSupplierCredentialDao(NewSupplierCredentialModel(conn)),
	}
}

func (d *Dao) Transact(ctx context.Context, fn func(context.Context, sqlx.Session) error) error {
	return d.conn.TransactCtx(ctx, fn)
}
