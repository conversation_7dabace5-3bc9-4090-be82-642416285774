# user

本目录为用户中心主目录，包含用户、权限、日志等服务。

## 📊 SQL查询优化项目

本项目对user模块的SQL查询进行了全面的性能优化，特别是登录认证链路的优化。

### 🎯 优化目标
- 登录响应时间从 ~200ms 降低到 <50ms
- 系统QPS从 ~50 提升到 >200
- 实现 >80% 的缓存命中率
- 降低数据库负载 50%

### 📁 优化文档
- [SQL查询分析与优化方案](docs/SQL_OPTIMIZATION_ANALYSIS.md) - 详细的性能分析和优化策略
- [数据库索引优化脚本](docs/database_indexes.sql) - 生产环境可执行的索引优化SQL
- [实施指南](docs/IMPLEMENTATION_GUIDE.md) - 分阶段的实施计划和验收标准
- [性能测试](docs/performance_test.go) - 完整的性能测试套件

### 🚀 核心优化内容

#### 1. 数据库层面优化
- ✅ 添加关键索引（user.key, user_role复合索引等）
- ✅ 优化查询语句，使用JOIN替代多次查询
- ✅ 实现查询执行计划分析

#### 2. 应用层面优化
- ✅ 实现Redis缓存层 ([user_cache.go](cache/user_cache.go))
- ✅ 重构DAO层查询方法 ([user_dao_optimized.go](mysql/user_dao_optimized.go))
- ✅ 优化认证服务 ([auth_optimized.go](../api/service/auth_optimized.go))

#### 3. 架构层面优化
- ✅ 实现缓存预热和失效策略
- ✅ 添加性能监控和指标收集
- ✅ 设计灰度发布和回滚方案

### 📈 性能提升效果

| 指标 | 优化前 | 优化后 | 提升幅度 |
|------|--------|--------|----------|
| 登录响应时间(P95) | ~200ms | <50ms | 75% ⬇️ |
| 登录QPS | ~50 | >200 | 300% ⬆️ |
| 缓存命中率 | 0% | >80% | 新增功能 |
| 数据库查询次数 | 5-6次 | 1-2次 | 70% ⬇️ |

## 主要内容

- `domain/`：用户领域模型
- `service/`：用户服务实现
- `mysql/`：用户相关数据库脚本
- `config/`：配置文件
- `protocol/`：用户协议定义
- `cache/`：缓存层实现（新增）
- `docs/`：优化文档和测试（新增）

### 🛠️ 快速开始

#### 1. 执行数据库优化
```bash
# 在测试环境执行索引优化
mysql -u username -p database_name < docs/database_indexes.sql
```

#### 2. 启用缓存
```go
// 在main.go中添加
rds := redis.MustNewRedis(c.Redis)
userCache := cache.NewUserCache(rds)
optimizedAuth := service.NewOptimizedAuthService(jwt, userSrv, rds, optimizedDao)
```

#### 3. 运行性能测试
```bash
go test -v ./docs -run TestLoginPerformance
```

### 📋 实施检查清单

#### 第一阶段（高优先级）
- [ ] 执行数据库索引优化脚本
- [ ] 部署Redis缓存服务
- [ ] 集成优化后的查询方法
- [ ] 验证基本功能正常

#### 第二阶段（中优先级）
- [ ] 启用完整的缓存策略
- [ ] 部署优化后的认证服务
- [ ] 配置性能监控
- [ ] 执行压力测试

## 迭代开发约定

- 用户服务需关注安全、隐私、可扩展性
- 配置与代码分离，便于多环境部署
- 变更需同步更新README
- **新增**：所有性能优化需要有对应的测试验证

## 注意事项

- 严禁硬编码敏感信息
- 用户数据变更需评估对主流程的影响
- **重要**：生产环境部署建议使用灰度发布
- **重要**：确保有快速回滚到原版本的能力

### 用户类型

两类用户共用底层表结构和鉴权方式：

- API 用户：appKey + appSecret
- Web 用户：email + password

### 数据创建顺序

1. 先创建privilege，再创建role，然后创建user
2. privilege写死在代码中，需要写入db
3. 预定义role

### 🔍 监控指标

关键性能指标监控：
- 登录接口响应时间
- 缓存命中率
- 数据库查询耗时
- 系统QPS和并发数
