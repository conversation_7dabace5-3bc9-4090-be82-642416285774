package service

import (
	"context"

	"hotel/common/bff"
	"hotel/common/bizerr"
	"hotel/common/types"
	"hotel/user/domain"
	"hotel/user/protocol"
)

// UpdateUser
// @path: /updateUser
// @auth: required
// @tags: admin.hotelbyte.com
// @desc: 更新用户基本信息、角色状态等
// @response: ,CrossTenantErr,失败
func (s *UserService) UpdateUser(ctx context.Context, req *protocol.UpdateUserReq) (*protocol.UpdateUserResp, error) {
	if req.TargetUserId == 0 {
		return nil, bizerr.New(400, "目标用户ID不能为空")
	}

	// 检查目标用户是否存在
	targetUser, err := s.dao.User.GetUser(ctx, req.TargetUserId)
	if err != nil {
		return nil, err
	}
	if targetUser == nil {
		return nil, bizerr.New(404, "用户不存在")
	}

	// 处理每个action
	for _, action := range req.Actions {
		switch action.Action {
		case bff.ActionId_UpdateUserBasicProfile:
			if err := s.handleUpdateUserBasic(ctx, req.TargetUserId, action.Params); err != nil {
				return nil, err
			}
		case bff.ActionId_UpdateUserRoleStatus:
			if err := s.handleUpdateUserRoleStatus(ctx, req.TargetUserId, action.Params); err != nil {
				return nil, err
			}
		default:
			return nil, bizerr.New(400, "不支持的操作类型")
		}
	}

	return &protocol.UpdateUserResp{}, nil
}

// handleUpdateUserBasic 处理用户基本信息更新
func (s *UserService) handleUpdateUserBasic(ctx context.Context, userID types.ID, params interface{}) error {
	basicValue, ok := params.(protocol.UpdateUserBasicValue)
	if !ok {
		return bizerr.New(400, "参数类型错误")
	}

	// 验证基本信息
	if basicValue.Username == "" {
		return bizerr.New(400, "用户名不能为空")
	}
	if basicValue.Key == "" {
		return bizerr.New(400, "邮箱不能为空")
	}

	// 检查邮箱是否已被其他用户使用
	if basicValue.Key != "" {
		existingUser, err := s.dao.User.GetUserByKey(ctx, basicValue.Key)
		if err != nil {
			return err
		}
		if existingUser != nil && existingUser.UserBasic.ID != userID {
			return bizerr.New(400, "邮箱已被其他用户使用")
		}
	}

	// 更新用户基本信息
	basicValue.ID = userID
	userBasic := domain.UserBasic(basicValue)
	return s.dao.User.UpdateUserBasic(ctx, &userBasic)
}

// handleUpdateUserRoleStatus 处理用户角色状态更新
func (s *UserService) handleUpdateUserRoleStatus(ctx context.Context, userID types.ID, params interface{}) error {
	roleStatusValue, ok := params.(protocol.UpdateUserRoleStatusValue)
	if !ok {
		return bizerr.New(400, "参数类型错误")
	}

	// 验证角色状态数据
	if len(roleStatusValue) == 0 {
		return bizerr.New(400, "角色状态数据不能为空")
	}

	// 更新用户角色状态
	return s.dao.User.UpdateUserRoles(ctx, userID.Int64(), []domain.UpdateUserRole(roleStatusValue))
}

func (s *UserService) JwtUpdater(ctx context.Context, in *domain.UserEntityConnection) error {
	in.Entity, _ = s.dao.Entity.Get(ctx, in.Entity.ID)
	if l := in.Link; l != nil {
		in.Link, _ = s.dao.User.GetEntityUserLink(ctx, l.UserId, l.EntityId)
		return nil
	}
	//todo: roles
	return nil
}
