package cache

import (
	"context"
	"encoding/json"
	"time"

	"hotel/common/types"
	"hotel/user/domain"

	"github.com/zeromicro/go-zero/core/stores/redis"
)

// 缓存键前缀
const (
	UserCacheKeyPrefix   = "user:"
	UserBasicKeyPrefix   = "user_basic:"
	UserRoleKeyPrefix    = "user_role:"
	EntityKeyPrefix      = "entity:"
	UserSessionKeyPrefix = "user_session:"

	// 缓存TTL
	UserCacheTTL      = 30 * time.Minute
	UserBasicCacheTTL = 15 * time.Minute
	UserRoleCacheTTL  = 15 * time.Minute
	EntityCacheTTL    = 60 * time.Minute
	UserSessionTTL    = 24 * time.Hour
)

// UserCache 用户缓存接口
type UserCache interface {
	// 用户基本信息缓存
	GetUserBasic(ctx context.Context, key string) (*domain.UserBasic, error)
	SetUserBasic(ctx context.Context, key string, user *domain.UserBasic) error
	DelUserBasic(ctx context.Context, key string) error

	// 完整用户信息缓存
	GetUser(ctx context.Context, userID types.ID) (*domain.User, error)
	SetUser(ctx context.Context, userID types.ID, user *domain.User) error
	DelUser(ctx context.Context, userID types.ID) error

	// 用户会话缓存
	GetUserSession(ctx context.Context, token string) (*domain.User, error)
	SetUserSession(ctx context.Context, token string, user *domain.User) error
	DelUserSession(ctx context.Context, token string) error

	// 实体信息缓存
	GetEntity(ctx context.Context, entityID types.ID) (*domain.Entity, error)
	SetEntity(ctx context.Context, entityID types.ID, entity *domain.Entity) error
	DelEntity(ctx context.Context, entityID types.ID) error

	// 批量删除缓存
	DelUserRelatedCache(ctx context.Context, userID types.ID) error
}

// redisUserCache Redis实现的用户缓存
type redisUserCache struct {
	rds redis.Redis
}

// NewUserCache 创建用户缓存实例
func NewUserCache(rds redis.Redis) UserCache {
	return &redisUserCache{
		rds: rds,
	}
}

// CachedUserBasic 缓存的用户基本信息结构
type CachedUserBasic struct {
	User     *domain.UserBasic `json:"user"`
	CachedAt time.Time         `json:"cached_at"`
}

// CachedUser 缓存的完整用户信息结构
type CachedUser struct {
	User     *domain.User `json:"user"`
	CachedAt time.Time    `json:"cached_at"`
}

// CachedEntity 缓存的实体信息结构
type CachedEntity struct {
	Entity   *domain.Entity `json:"entity"`
	CachedAt time.Time      `json:"cached_at"`
}

// GetUserBasic 获取用户基本信息缓存
func (c *redisUserCache) GetUserBasic(ctx context.Context, key string) (*domain.UserBasic, error) {
	cacheKey := UserBasicKeyPrefix + key

	val, err := c.rds.GetCtx(ctx, cacheKey)
	if err != nil {
		return nil, err
	}

	var cached CachedUserBasic
	if err := json.Unmarshal([]byte(val), &cached); err != nil {
		return nil, err
	}

	// 检查缓存是否过期（双重保险）
	if time.Since(cached.CachedAt) > UserBasicCacheTTL {
		c.rds.DelCtx(ctx, cacheKey)
		return nil, redis.Nil
	}

	return cached.User, nil
}

// SetUserBasic 设置用户基本信息缓存
func (c *redisUserCache) SetUserBasic(ctx context.Context, key string, user *domain.UserBasic) error {
	cacheKey := UserBasicKeyPrefix + key

	cached := CachedUserBasic{
		User:     user,
		CachedAt: time.Now(),
	}

	data, err := json.Marshal(cached)
	if err != nil {
		return err
	}

	return c.rds.SetexCtx(ctx, cacheKey, string(data), int(UserBasicCacheTTL.Seconds()))
}

// DelUserBasic 删除用户基本信息缓存
func (c *redisUserCache) DelUserBasic(ctx context.Context, key string) error {
	cacheKey := UserBasicKeyPrefix + key
	_, err := c.rds.DelCtx(ctx, cacheKey)
	return err
}

// GetUser 获取完整用户信息缓存
func (c *redisUserCache) GetUser(ctx context.Context, userID types.ID) (*domain.User, error) {
	cacheKey := UserCacheKeyPrefix + userID.String()

	val, err := c.rds.GetCtx(ctx, cacheKey)
	if err != nil {
		return nil, err
	}

	var cached CachedUser
	if err := json.Unmarshal([]byte(val), &cached); err != nil {
		return nil, err
	}

	// 检查缓存是否过期
	if time.Since(cached.CachedAt) > UserCacheTTL {
		c.rds.DelCtx(ctx, cacheKey)
		return nil, redis.Nil
	}

	return cached.User, nil
}

// SetUser 设置完整用户信息缓存
func (c *redisUserCache) SetUser(ctx context.Context, userID types.ID, user *domain.User) error {
	cacheKey := UserCacheKeyPrefix + userID.String()

	cached := CachedUser{
		User:     user,
		CachedAt: time.Now(),
	}

	data, err := json.Marshal(cached)
	if err != nil {
		return err
	}

	return c.rds.SetexCtx(ctx, cacheKey, string(data), int(UserCacheTTL.Seconds()))
}

// DelUser 删除完整用户信息缓存
func (c *redisUserCache) DelUser(ctx context.Context, userID types.ID) error {
	cacheKey := UserCacheKeyPrefix + userID.String()
	_, err := c.rds.DelCtx(ctx, cacheKey)
	return err
}

// GetUserSession 获取用户会话缓存
func (c *redisUserCache) GetUserSession(ctx context.Context, token string) (*domain.User, error) {
	cacheKey := UserSessionKeyPrefix + token

	val, err := c.rds.GetCtx(ctx, cacheKey)
	if err != nil {
		return nil, err
	}

	var cached CachedUser
	if err := json.Unmarshal([]byte(val), &cached); err != nil {
		return nil, err
	}

	return cached.User, nil
}

// SetUserSession 设置用户会话缓存
func (c *redisUserCache) SetUserSession(ctx context.Context, token string, user *domain.User) error {
	cacheKey := UserSessionKeyPrefix + token

	cached := CachedUser{
		User:     user,
		CachedAt: time.Now(),
	}

	data, err := json.Marshal(cached)
	if err != nil {
		return err
	}

	return c.rds.SetexCtx(ctx, cacheKey, string(data), int(UserSessionTTL.Seconds()))
}

// DelUserSession 删除用户会话缓存
func (c *redisUserCache) DelUserSession(ctx context.Context, token string) error {
	cacheKey := UserSessionKeyPrefix + token
	_, err := c.rds.DelCtx(ctx, cacheKey)
	return err
}

// GetEntity 获取实体信息缓存
func (c *redisUserCache) GetEntity(ctx context.Context, entityID types.ID) (*domain.Entity, error) {
	cacheKey := EntityKeyPrefix + entityID.String()

	val, err := c.rds.GetCtx(ctx, cacheKey)
	if err != nil {
		return nil, err
	}

	var cached CachedEntity
	if err := json.Unmarshal([]byte(val), &cached); err != nil {
		return nil, err
	}

	// 检查缓存是否过期
	if time.Since(cached.CachedAt) > EntityCacheTTL {
		c.rds.DelCtx(ctx, cacheKey)
		return nil, redis.Nil
	}

	return cached.Entity, nil
}

// SetEntity 设置实体信息缓存
func (c *redisUserCache) SetEntity(ctx context.Context, entityID types.ID, entity *domain.Entity) error {
	cacheKey := EntityKeyPrefix + entityID.String()

	cached := CachedEntity{
		Entity:   entity,
		CachedAt: time.Now(),
	}

	data, err := json.Marshal(cached)
	if err != nil {
		return err
	}

	return c.rds.SetexCtx(ctx, cacheKey, string(data), int(EntityCacheTTL.Seconds()))
}

// DelEntity 删除实体信息缓存
func (c *redisUserCache) DelEntity(ctx context.Context, entityID types.ID) error {
	cacheKey := EntityKeyPrefix + entityID.String()
	_, err := c.rds.DelCtx(ctx, cacheKey)
	return err
}

// DelUserRelatedCache 删除用户相关的所有缓存
func (c *redisUserCache) DelUserRelatedCache(ctx context.Context, userID types.ID) error {
	// 删除用户完整信息缓存
	userKey := UserCacheKeyPrefix + userID.String()

	// 删除用户基本信息缓存（需要先获取用户的key）
	// 这里可能需要额外的查询来获取用户的key，或者维护一个反向映射

	keys := []string{userKey}

	_, err := c.rds.DelCtx(ctx, keys...)
	return err
}

// CacheStats 缓存统计信息
type CacheStats struct {
	UserBasicHits   int64 `json:"user_basic_hits"`
	UserBasicMisses int64 `json:"user_basic_misses"`
	UserHits        int64 `json:"user_hits"`
	UserMisses      int64 `json:"user_misses"`
	EntityHits      int64 `json:"entity_hits"`
	EntityMisses    int64 `json:"entity_misses"`
	SessionHits     int64 `json:"session_hits"`
	SessionMisses   int64 `json:"session_misses"`
}

// GetCacheStats 获取缓存统计信息（需要配合监控系统）
func (c *redisUserCache) GetCacheStats(ctx context.Context) (*CacheStats, error) {
	// 这里可以实现缓存命中率统计
	// 需要在每次Get操作时记录命中/未命中情况
	return &CacheStats{}, nil
}

// WarmupCache 缓存预热
func (c *redisUserCache) WarmupCache(ctx context.Context, userIDs []types.ID) error {
	// 这里可以实现缓存预热逻辑
	// 批量加载热点用户数据到缓存
	return nil
}

// CacheWithFallback 带回退的缓存获取模式
type CacheWithFallback struct {
	cache        UserCache
	fallbackFunc func(ctx context.Context) (interface{}, error)
}

// GetWithFallback 获取数据，如果缓存未命中则调用回退函数
func (c *CacheWithFallback) GetWithFallback(ctx context.Context, key string) (interface{}, error) {
	// 先尝试从缓存获取
	// 如果缓存未命中，调用回退函数获取数据，并设置缓存
	return nil, nil
}

// BatchCache 批量缓存操作
type BatchCache struct {
	cache UserCache
}

// GetUsersBatch 批量获取用户信息
func (b *BatchCache) GetUsersBatch(ctx context.Context, userIDs []types.ID) (map[types.ID]*domain.User, []types.ID, error) {
	found := make(map[types.ID]*domain.User)
	missing := make([]types.ID, 0)

	for _, userID := range userIDs {
		user, err := b.cache.GetUser(ctx, userID)
		if err == redis.Nil {
			missing = append(missing, userID)
		} else if err != nil {
			return nil, nil, err
		} else {
			found[userID] = user
		}
	}

	return found, missing, nil
}

// SetUsersBatch 批量设置用户信息缓存
func (b *BatchCache) SetUsersBatch(ctx context.Context, users map[types.ID]*domain.User) error {
	for userID, user := range users {
		if err := b.cache.SetUser(ctx, userID, user); err != nil {
			return err
		}
	}
	return nil
}

// CacheMetrics 缓存指标
type CacheMetrics struct {
	HitCount   int64
	MissCount  int64
	ErrorCount int64
}

// GetHitRate 获取缓存命中率
func (m *CacheMetrics) GetHitRate() float64 {
	total := m.HitCount + m.MissCount
	if total == 0 {
		return 0
	}
	return float64(m.HitCount) / float64(total)
}
