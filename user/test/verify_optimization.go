package main

import (
	"context"
	"fmt"
	"log"
	"time"

	"github.com/zeromicro/go-zero/core/conf"
	"github.com/zeromicro/go-zero/core/stores/redis"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"hotel/common/types"
	"hotel/user/cache"
	"hotel/user/config"
	"hotel/user/domain"
	"hotel/user/mysql"
)

func main() {
	fmt.Println("=== User模块SQL优化验证 ===")
	
	// 加载配置
	var cfg config.Config
	conf.MustLoad("../config/config.yaml", &cfg)
	
	// 创建数据库连接
	userDB := sqlx.NewMysql(cfg.MySQL.User)
	dao := mysql.NewDao(userDB)
	
	// 创建Redis连接
	rds := redis.MustNewRedis(redis.RedisConf{
		Host: "localhost:6379",
		Type: "node",
	})
	userCache := cache.NewUserCache(*rds)
	
	ctx := context.Background()
	testUserKey := "<EMAIL>"
	testUserID := types.ID(999)
	
	// 创建测试用户对象
	testUser := &domain.User{
		UserBasic: &domain.UserBasic{
			ID:       testUserID,
			Key:      testUserKey,
			Username: "test_performance",
			Secret:   "$2a$10$hash",
		},
		UserEntityConnections: []*domain.UserEntityConnection{},
	}
	
	fmt.Println("\n1. 测试数据库索引优化效果")
	testDatabaseIndexes(dao, ctx, testUserKey)
	
	fmt.Println("\n2. 测试缓存性能")
	testCachePerformance(userCache, ctx, testUserID, testUser)
	
	fmt.Println("\n3. 测试优化后的DAO方法")
	testOptimizedDAO(dao, ctx, testUserKey, testUserID)
	
	fmt.Println("\n4. 测试缓存vs数据库性能对比")
	testCacheVsDatabase(dao, userCache, ctx, testUserKey, testUserID, testUser)
	
	fmt.Println("\n5. 系统健康检查")
	testSystemHealth(dao, userCache, ctx)
	
	fmt.Println("\n=== 优化验证完成 ===")
}

func testDatabaseIndexes(dao *mysql.Dao, ctx context.Context, testUserKey string) {
	fmt.Println("测试按key查询（应该使用索引）...")
	
	start := time.Now()
	_, err := dao.UserOptimized.GetUserBasicByKey(ctx, testUserKey)
	duration := time.Since(start)
	
	fmt.Printf("查询耗时: %v\n", duration)
	
	if err == mysql.ErrNotFound {
		fmt.Println("✓ 用户不存在（预期结果）")
	} else if err != nil {
		fmt.Printf("✗ 查询错误: %v\n", err)
	} else {
		fmt.Println("✓ 查询成功")
	}
	
	if duration.Milliseconds() < 1000 {
		fmt.Println("✓ 查询性能良好（<1s）")
	} else {
		fmt.Println("⚠ 查询性能需要改进（>1s）")
	}
}

func testCachePerformance(userCache cache.UserCache, ctx context.Context, testUserID types.ID, testUser *domain.User) {
	fmt.Println("测试缓存操作性能...")
	
	// 测试缓存设置
	start := time.Now()
	err := userCache.SetUser(ctx, testUserID, testUser)
	setDuration := time.Since(start)
	
	if err != nil {
		fmt.Printf("✗ 缓存设置失败: %v\n", err)
		return
	}
	
	fmt.Printf("缓存设置耗时: %v\n", setDuration)
	
	// 测试缓存获取
	start = time.Now()
	cachedUser, err := userCache.GetUser(ctx, testUserID)
	getDuration := time.Since(start)
	
	if err != nil {
		fmt.Printf("✗ 缓存获取失败: %v\n", err)
		return
	}
	
	fmt.Printf("缓存获取耗时: %v\n", getDuration)
	
	if cachedUser.ID == testUser.ID {
		fmt.Println("✓ 缓存数据正确")
	} else {
		fmt.Println("✗ 缓存数据不匹配")
	}
	
	// 性能评估
	if setDuration.Milliseconds() < 50 {
		fmt.Println("✓ 缓存设置性能优秀（<50ms）")
	} else {
		fmt.Println("⚠ 缓存设置性能需要改进（>50ms）")
	}
	
	if getDuration.Milliseconds() < 10 {
		fmt.Println("✓ 缓存获取性能优秀（<10ms）")
	} else {
		fmt.Println("⚠ 缓存获取性能需要改进（>10ms）")
	}
}

func testOptimizedDAO(dao *mysql.Dao, ctx context.Context, testUserKey string, testUserID types.ID) {
	fmt.Println("测试优化后的DAO方法...")
	
	// 测试GetUserBasicByKey
	start := time.Now()
	userBasic, err := dao.UserOptimized.GetUserBasicByKey(ctx, testUserKey)
	basicDuration := time.Since(start)
	
	fmt.Printf("GetUserBasicByKey耗时: %v\n", basicDuration)
	
	if err == mysql.ErrNotFound {
		fmt.Println("✓ GetUserBasicByKey正常工作")
	} else if err != nil {
		fmt.Printf("✗ GetUserBasicByKey错误: %v\n", err)
	} else {
		fmt.Printf("✓ 获取到用户: %s\n", userBasic.Key)
	}
	
	// 测试GetUserOptimized
	start = time.Now()
	user, err := dao.UserOptimized.GetUserOptimized(ctx, testUserID.Int64())
	optimizedDuration := time.Since(start)
	
	fmt.Printf("GetUserOptimized耗时: %v\n", optimizedDuration)
	
	if err == mysql.ErrNotFound {
		fmt.Println("✓ GetUserOptimized正常工作")
	} else if err != nil {
		fmt.Printf("✗ GetUserOptimized错误: %v\n", err)
	} else {
		fmt.Printf("✓ 获取到完整用户信息: %s\n", user.Key)
	}
}

func testCacheVsDatabase(dao *mysql.Dao, userCache cache.UserCache, ctx context.Context, testUserKey string, testUserID types.ID, testUser *domain.User) {
	fmt.Println("测试缓存vs数据库性能对比...")
	
	// 预热缓存
	userCache.SetUser(ctx, testUserID, testUser)
	
	// 测试数据库查询
	dbStart := time.Now()
	_, dbErr := dao.UserOptimized.GetUserBasicByKey(ctx, testUserKey)
	dbDuration := time.Since(dbStart)
	
	// 测试缓存查询
	cacheStart := time.Now()
	_, cacheErr := userCache.GetUser(ctx, testUserID)
	cacheDuration := time.Since(cacheStart)
	
	fmt.Printf("数据库查询耗时: %v\n", dbDuration)
	fmt.Printf("缓存查询耗时: %v\n", cacheDuration)
	
	if dbErr != nil && dbErr != mysql.ErrNotFound {
		fmt.Printf("⚠ 数据库查询错误: %v\n", dbErr)
	}
	
	if cacheErr != nil {
		fmt.Printf("✗ 缓存查询错误: %v\n", cacheErr)
		return
	}
	
	if cacheDuration < dbDuration {
		speedup := float64(dbDuration) / float64(cacheDuration)
		fmt.Printf("✓ 缓存比数据库快 %.2f 倍\n", speedup)
		
		improvement := float64(dbDuration-cacheDuration) / float64(dbDuration) * 100
		fmt.Printf("✓ 性能提升: %.2f%%\n", improvement)
	} else {
		fmt.Println("⚠ 缓存性能未达到预期")
	}
}

func testSystemHealth(dao *mysql.Dao, userCache cache.UserCache, ctx context.Context) {
	fmt.Println("测试系统健康状态...")
	
	// 测试数据库连接
	dbStart := time.Now()
	_, err := dao.UserOptimized.GetUserBasicByKey(ctx, "health_check_user")
	dbHealthDuration := time.Since(dbStart)
	
	if err == mysql.ErrNotFound {
		fmt.Printf("✓ 数据库连接正常 (耗时: %v)\n", dbHealthDuration)
	} else if err != nil {
		fmt.Printf("✗ 数据库连接异常: %v\n", err)
	} else {
		fmt.Printf("✓ 数据库连接正常 (耗时: %v)\n", dbHealthDuration)
	}
	
	// 测试Redis连接
	testUser := &domain.User{
		UserBasic: &domain.UserBasic{
			ID:  types.ID(1),
			Key: "health_check",
		},
	}
	
	redisStart := time.Now()
	err = userCache.SetUser(ctx, types.ID(1), testUser)
	redisHealthDuration := time.Since(redisStart)
	
	if err != nil {
		fmt.Printf("✗ Redis连接异常: %v\n", err)
	} else {
		fmt.Printf("✓ Redis连接正常 (耗时: %v)\n", redisHealthDuration)
		
		// 清理测试数据
		userCache.DelUser(ctx, types.ID(1))
	}
	
	// 总体健康评估
	if dbHealthDuration.Milliseconds() < 1000 && redisHealthDuration.Milliseconds() < 100 {
		fmt.Println("✓ 系统整体健康状态良好")
	} else {
		fmt.Println("⚠ 系统性能需要关注")
	}
}

func init() {
	log.SetFlags(log.LstdFlags | log.Lshortfile)
}
