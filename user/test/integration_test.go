package test

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/zeromicro/go-zero/core/conf"
	"github.com/zeromicro/go-zero/core/stores/redis"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"hotel/api/config"
	"hotel/api/service"
	httpUtil "hotel/common/httpdispatcher"
	"hotel/common/types"
	"hotel/user/cache"
	userConfig "hotel/user/config"
	"hotel/user/domain"
	"hotel/user/mysql"
	userService "hotel/user/service"
)

// IntegrationTestSetup 集成测试设置
type IntegrationTestSetup struct {
	userSrv           *userService.UserService
	optimizedAuthSrv  *service.SimpleOptimizedAuthService
	dao               *mysql.Dao
	userCache         cache.UserCache
	testUser          *domain.User
}

// setupIntegrationTest 设置集成测试环境
func setupIntegrationTest(t *testing.T) *IntegrationTestSetup {
	// 加载用户服务配置
	var userCfg userConfig.Config
	conf.MustLoad("../config/config.yaml", &userCfg)
	
	// 加载API配置
	var apiCfg config.Config
	conf.MustLoad("../../api/config/config.dev.yaml", &apiCfg)
	
	// 创建数据库连接
	userDB := sqlx.NewMysql(userCfg.MySQL.User)
	dao := mysql.NewDao(userDB)
	
	// 创建Redis连接
	rds := redis.MustNewRedis(apiCfg.Redis)
	userCache := cache.NewUserCache(*rds)
	
	// 创建JWT插件
	jwtAuth, err := httpUtil.NewJWTPlugin(apiCfg.JwtAuth)
	assert.NoError(t, err)
	
	// 创建用户服务
	userSrv := userService.NewUserService()
	
	// 创建优化后的认证服务
	optimizedAuthSrv := service.NewSimpleOptimizedAuthService(jwtAuth, userSrv, apiCfg)
	
	// 创建测试用户
	testUser := &domain.User{
		UserBasic: &domain.UserBasic{
			ID:       types.ID(1001),
			Key:      "<EMAIL>",
			Username: "integration_test",
			Secret:   "$2a$10$hash",
		},
		UserEntityConnections: []*domain.UserEntityConnection{},
	}
	
	return &IntegrationTestSetup{
		userSrv:          userSrv,
		optimizedAuthSrv: optimizedAuthSrv,
		dao:              dao,
		userCache:        userCache,
		testUser:         testUser,
	}
}

// TestEndToEndOptimization 端到端优化测试
func TestEndToEndOptimization(t *testing.T) {
	setup := setupIntegrationTest(t)
	ctx := context.Background()
	
	// 确保测试用户存在
	err := setup.dao.Transact(ctx, func(ctx context.Context, tx sqlx.Session) error {
		return setup.dao.User.TxUpsertUser(ctx, tx, setup.testUser.UserBasic)
	})
	assert.NoError(t, err)
	
	t.Run("Complete Optimized Flow", func(t *testing.T) {
		startTime := time.Now()
		
		// 1. 测试缓存预热
		err := setup.userCache.SetUser(ctx, setup.testUser.ID, setup.testUser)
		assert.NoError(t, err)
		
		// 2. 测试从缓存获取用户信息
		cachedUser, err := setup.userCache.GetUser(ctx, setup.testUser.ID)
		assert.NoError(t, err)
		assert.Equal(t, setup.testUser.ID, cachedUser.ID)
		
		// 3. 测试优化后的用户基本信息获取
		userBasic, err := setup.userSrv.GetUserBasicOptimized(ctx, setup.testUser.Key)
		assert.NoError(t, err)
		assert.NotNil(t, userBasic)
		assert.Equal(t, setup.testUser.Key, userBasic.Key)
		
		// 4. 测试健康检查
		err = setup.optimizedAuthSrv.HealthCheck(ctx)
		assert.NoError(t, err)
		
		// 5. 测试缓存统计
		stats, err := setup.optimizedAuthSrv.GetCacheStats(ctx)
		assert.NoError(t, err)
		assert.True(t, stats["redis_connected"].(bool))
		assert.True(t, stats["cache_enabled"].(bool))
		
		totalDuration := time.Since(startTime)
		t.Logf("Complete optimized flow took: %v", totalDuration)
		
		// 完整流程应该在合理时间内完成
		assert.Less(t, totalDuration.Milliseconds(), int64(1000), "Complete flow should finish within 1s")
	})
}

// TestCacheEffectiveness 测试缓存有效性
func TestCacheEffectiveness(t *testing.T) {
	setup := setupIntegrationTest(t)
	ctx := context.Background()
	
	t.Run("Cache Hit vs Miss Performance", func(t *testing.T) {
		// 测试缓存未命中
		missStart := time.Now()
		_, err := setup.userCache.GetUser(ctx, types.ID(99999)) // 不存在的用户
		missDuration := time.Since(missStart)
		
		// 应该返回redis.Nil错误
		assert.Equal(t, redis.Nil, err)
		
		// 测试缓存命中
		setup.userCache.SetUser(ctx, setup.testUser.ID, setup.testUser)
		
		hitStart := time.Now()
		cachedUser, err := setup.userCache.GetUser(ctx, setup.testUser.ID)
		hitDuration := time.Since(hitStart)
		
		assert.NoError(t, err)
		assert.NotNil(t, cachedUser)
		
		t.Logf("Cache miss: %v, Cache hit: %v", missDuration, hitDuration)
		
		// 缓存命中应该比未命中快
		assert.Less(t, hitDuration, missDuration)
		
		// 缓存命中应该很快
		assert.Less(t, hitDuration.Milliseconds(), int64(10), "Cache hit should be very fast")
	})
}

// TestOptimizationBenefits 测试优化收益
func TestOptimizationBenefits(t *testing.T) {
	setup := setupIntegrationTest(t)
	ctx := context.Background()
	
	// 确保测试用户存在
	err := setup.dao.Transact(ctx, func(ctx context.Context, tx sqlx.Session) error {
		return setup.dao.User.TxUpsertUser(ctx, tx, setup.testUser.UserBasic)
	})
	assert.NoError(t, err)
	
	t.Run("Database Index Benefits", func(t *testing.T) {
		// 测试索引查询性能
		startTime := time.Now()
		
		// 执行3次查询来平均化结果
		for i := 0; i < 3; i++ {
			_, err := setup.dao.User.GetUserByKey(ctx, setup.testUser.Key)
			if err != nil && err != mysql.ErrNotFound {
				t.Logf("Query %d failed: %v", i, err)
			}
		}
		
		duration := time.Since(startTime)
		avgDuration := duration / 3
		
		t.Logf("Indexed query average: %v", avgDuration)
		
		// 有索引的查询应该相对较快（考虑到网络延迟）
		assert.Less(t, avgDuration.Milliseconds(), int64(500), "Indexed queries should be reasonably fast")
	})
	
	t.Run("Cache vs Database Performance", func(t *testing.T) {
		// 预热缓存
		setup.userCache.SetUser(ctx, setup.testUser.ID, setup.testUser)
		
		// 测试数据库查询
		dbStart := time.Now()
		_, err := setup.dao.User.GetUserByKey(ctx, setup.testUser.Key)
		dbDuration := time.Since(dbStart)
		
		if err != nil && err != mysql.ErrNotFound {
			t.Logf("Database query failed: %v", err)
		}
		
		// 测试缓存查询
		cacheStart := time.Now()
		_, err = setup.userCache.GetUser(ctx, setup.testUser.ID)
		cacheDuration := time.Since(cacheStart)
		
		assert.NoError(t, err)
		
		t.Logf("Database query: %v, Cache query: %v", dbDuration, cacheDuration)
		
		// 缓存应该比数据库快得多
		assert.Less(t, cacheDuration, dbDuration)
		
		// 计算性能提升
		if dbDuration > 0 {
			improvement := float64(dbDuration-cacheDuration) / float64(dbDuration) * 100
			t.Logf("Cache performance improvement: %.2f%%", improvement)
			
			// 缓存应该有显著的性能提升
			assert.Greater(t, improvement, 50.0, "Cache should provide significant performance improvement")
		}
	})
}

// TestSystemResilience 测试系统弹性
func TestSystemResilience(t *testing.T) {
	setup := setupIntegrationTest(t)
	ctx := context.Background()
	
	t.Run("Cache Failure Handling", func(t *testing.T) {
		// 测试缓存失败时的降级处理
		// 这里我们测试获取不存在的用户
		_, err := setup.userCache.GetUser(ctx, types.ID(99999))
		
		// 应该返回redis.Nil而不是崩溃
		assert.Equal(t, redis.Nil, err)
	})
	
	t.Run("Health Check", func(t *testing.T) {
		// 测试健康检查
		err := setup.optimizedAuthSrv.HealthCheck(ctx)
		assert.NoError(t, err, "Health check should pass")
	})
}

// TestPerformanceMetrics 测试性能指标
func TestPerformanceMetrics(t *testing.T) {
	setup := setupIntegrationTest(t)
	ctx := context.Background()
	
	t.Run("Performance Metrics Collection", func(t *testing.T) {
		// 执行一系列操作并收集性能指标
		metrics := make(map[string]time.Duration)
		
		// 缓存设置性能
		start := time.Now()
		setup.userCache.SetUser(ctx, setup.testUser.ID, setup.testUser)
		metrics["cache_set"] = time.Since(start)
		
		// 缓存获取性能
		start = time.Now()
		setup.userCache.GetUser(ctx, setup.testUser.ID)
		metrics["cache_get"] = time.Since(start)
		
		// 缓存删除性能
		start = time.Now()
		setup.userCache.DelUser(ctx, setup.testUser.ID)
		metrics["cache_del"] = time.Since(start)
		
		// 输出性能指标
		for operation, duration := range metrics {
			t.Logf("%s: %v", operation, duration)
			
			// 所有缓存操作都应该很快
			assert.Less(t, duration.Milliseconds(), int64(50), 
				"Cache operation %s should be fast", operation)
		}
	})
}
