# Hotel Booking Platform

酒店预订平台 - 一个现代化的酒店分销和预订管理系统

## 项目概述

本项目是一个完整的酒店预订平台，包含前端管理界面和后端API服务。系统支持多供应商接入、酒店搜索、订单管理、用户管理等功能。

## 技术栈

### 后端
- **语言**: Go 1.21+
- **框架**: go-zero
- **数据库**: MySQL 8.0+
- **缓存**: Redis 6.0+
- **认证**: JWT

### 前端
- **框架**: Vue 3 + TypeScript
- **UI库**: Element Plus
- **路由**: Vue Router 4
- **状态管理**: Pinia
- **构建工具**: Vite

## 功能模块

### 1. 用户管理模块 ✅
- **租户用户管理**: 支持租户用户的增删改查
- **客户用户管理**: 支持客户用户的增删改查  
- **平台用户管理**: 支持平台用户的增删改查
- **个人中心**: 支持用户个人资料编辑
- **用户编辑**: 支持用户信息编辑功能

#### 最近修复 (2024-01-15)
- ✅ 完善后端UpdateUser接口实现
- ✅ 添加用户基本信息更新功能
- ✅ 添加用户角色状态更新功能
- ✅ 完善前端个人中心API集成
- ✅ 完善前端用户编辑页面API集成
- ✅ 修复用户模块404页面问题
- ✅ 添加用户管理端到端测试

### 2. 酒店搜索模块
- 多供应商酒店数据聚合
- 智能搜索和筛选
- 价格比较和排序

### 3. 订单管理模块
- 订单创建和确认
- 订单状态跟踪
- 订单历史查询

### 4. 实体管理模块
- 租户实体管理
- 供应商实体管理
- 客户实体管理

## 快速开始

### 环境要求
- Go 1.21+
- Node.js 18+
- MySQL 8.0+
- Redis 6.0+

### 后端启动
```bash
# 安装依赖
go mod download

# 配置数据库
cp config/config.dev.yaml.example config/config.dev.yaml
# 编辑配置文件

# 启动服务
go run cmd/main.go
```

### 前端启动
```bash
cd admin-fe

# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

## 开发指南

### 代码规范
- 使用ESLint和Prettier进行代码格式化
- 遵循Go官方代码规范
- 使用TypeScript进行类型检查

### 测试
- 后端使用Go标准测试框架
- 前端使用Vitest进行单元测试
- 使用Playwright进行端到端测试

### API文档
- 使用OpenAPI 3.0规范
- 支持在线API文档查看
- 提供完整的接口示例

## 部署

### Docker部署
```bash
# 构建镜像
docker build -t hotel-platform .

# 运行容器
docker run -d -p 8080:8080 hotel-platform
```

### 生产环境配置
- 使用环境变量管理配置
- 支持多环境部署
- 提供完整的监控和日志

## 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交代码
4. 创建Pull Request

## 许可证

MIT License

## 联系方式

- 项目维护者: Danceiny
- 邮箱: <EMAIL>
- GitHub: https://github.com/danceiny/hotel-be

## 更新日志

### v1.0.0 (2024-01-15)
- 🎉 初始版本发布
- ✅ 完成用户管理模块
- ✅ 完成酒店搜索基础功能
- ✅ 完成订单管理基础功能
- ✅ 完成实体管理基础功能
- ✅ 添加完整的端到端测试
- ✅ 修复用户模块404问题
- ✅ 完善个人资料编辑功能
