package service

import (
	"context"
	"fmt"
	"time"

	"hotel/bi/protocol"
	"hotel/common/log"
)

// OrderBIService BI订单分析API服务
type OrderBIService struct {
	analyticsService *OrderAnalyticsService
}

// NewOrderBIService 创建BI订单服务
func NewOrderBIService(analyticsService *OrderAnalyticsService) *OrderBIService {
	return &OrderBIService{
		analyticsService: analyticsService,
	}
}

// Name 实现httpdispatcher.Service接口
func (s *OrderBIService) Name() string {
	return "bi"
}

// GetOrderAnalytics 获取订单分析数据
func (s *OrderBIService) GetOrderAnalytics(ctx context.Context, req *protocol.OrderAnalyticsReq) (*protocol.OrderAnalyticsResp, error) {
	log.Infoc(ctx, "GetOrderAnalytics request: %+v", req)

	resp, err := s.analyticsService.GetOrderAnalytics(ctx, req)
	if err != nil {
		log.Errorc(ctx, "Failed to get order analytics: %v", err)
		return nil, err
	}

	log.Infoc(ctx, "GetOrderAnalytics response: overview orders=%d, trends=%d",
		resp.Overview.TotalOrders, len(resp.TrendData))

	return resp, nil
}

// GetOrderMetrics 获取订单指标
func (s *OrderBIService) GetOrderMetrics(ctx context.Context, req *protocol.OrderMetricsReq) (*protocol.OrderMetricsResp, error) {
	log.Infoc(ctx, "GetOrderMetrics request: %+v", req)

	var data interface{}
	var err error

	switch req.MetricType {
	case "overview":
		data, err = s.getOverviewMetrics(ctx, req)
	case "trend":
		data, err = s.getTrendMetrics(ctx, req)
	case "status":
		data, err = s.getStatusMetrics(ctx, req)
	case "revenue":
		data, err = s.getRevenueMetrics(ctx, req)
	default:
		return nil, fmt.Errorf("unsupported metric type: %s", req.MetricType)
	}

	if err != nil {
		log.Errorc(ctx, "Failed to get %s metrics: %v", req.MetricType, err)
		return nil, err
	}

	return &protocol.OrderMetricsResp{
		MetricType: req.MetricType,
		Data:       data,
		UpdateTime: time.Now(),
	}, nil
}

// GetRealTimeMetrics 获取实时指标
func (s *OrderBIService) GetRealTimeMetrics(ctx context.Context, req *protocol.RealTimeMetricsReq) (*protocol.RealTimeMetricsResp, error) {
	log.Infoc(ctx, "GetRealTimeMetrics request: %+v", req)

	resp, err := s.analyticsService.GetRealTimeMetrics(ctx, req)
	if err != nil {
		log.Errorc(ctx, "Failed to get real-time metrics: %v", err)
		return nil, err
	}

	log.Infoc(ctx, "GetRealTimeMetrics response: today orders=%d, revenue=%d",
		resp.TodayOrders, resp.TodayRevenue.Amount)

	return resp, nil
}

// ExportData 导出数据
func (s *OrderBIService) ExportData(ctx context.Context, req *protocol.ExportReq) (*protocol.ExportResp, error) {
	log.Infoc(ctx, "ExportData request: %+v", req)

	// 生成文件名
	timestamp := time.Now().Format("20060102_150405")
	fileName := fmt.Sprintf("order_%s_%s.%s", req.DataType, timestamp, req.ExportType)

	// 模拟文件URL（实际应该上传到文件存储服务）
	fileUrl := fmt.Sprintf("/api/bi/download/%s", fileName)

	return &protocol.ExportResp{
		FileUrl:    fileUrl,
		FileName:   fileName,
		FileSize:   1024,                           // 模拟文件大小
		ExpiryTime: time.Now().Add(24 * time.Hour), // 24小时后过期
	}, nil
}

// getOverviewMetrics 获取总览指标
func (s *OrderBIService) getOverviewMetrics(ctx context.Context, req *protocol.OrderMetricsReq) (interface{}, error) {
	startDate := req.StartDate
	endDate := req.EndDate
	if startDate == nil {
		t := time.Now().AddDate(0, -1, 0)
		startDate = &t
	}
	if endDate == nil {
		t := time.Now()
		endDate = &t
	}

	analyticsReq := &protocol.OrderAnalyticsReq{
		StartDate: startDate,
		EndDate:   endDate,
	}

	// 提取EntityId过滤器
	if entityId, ok := req.Filters["entityId"].(float64); ok {
		id := int64(entityId)
		analyticsReq.EntityId = &id
	}

	resp, err := s.analyticsService.GetOrderAnalytics(ctx, analyticsReq)
	if err != nil {
		return nil, err
	}

	return resp.Overview, nil
}

// getTrendMetrics 获取趋势指标
func (s *OrderBIService) getTrendMetrics(ctx context.Context, req *protocol.OrderMetricsReq) (interface{}, error) {
	startDate := req.StartDate
	endDate := req.EndDate
	if startDate == nil {
		t := time.Now().AddDate(0, -1, 0)
		startDate = &t
	}
	if endDate == nil {
		t := time.Now()
		endDate = &t
	}

	granularity := req.Granularity
	if granularity == "" {
		granularity = "day"
	}

	analyticsReq := &protocol.OrderAnalyticsReq{
		StartDate:   startDate,
		EndDate:     endDate,
		Granularity: granularity,
	}

	// 提取EntityId过滤器
	if entityId, ok := req.Filters["entityId"].(float64); ok {
		id := int64(entityId)
		analyticsReq.EntityId = &id
	}

	resp, err := s.analyticsService.GetOrderAnalytics(ctx, analyticsReq)
	if err != nil {
		return nil, err
	}

	return resp.TrendData, nil
}

// getStatusMetrics 获取状态指标
func (s *OrderBIService) getStatusMetrics(ctx context.Context, req *protocol.OrderMetricsReq) (interface{}, error) {
	startDate := req.StartDate
	endDate := req.EndDate
	if startDate == nil {
		t := time.Now().AddDate(0, -1, 0)
		startDate = &t
	}
	if endDate == nil {
		t := time.Now()
		endDate = &t
	}

	analyticsReq := &protocol.OrderAnalyticsReq{
		StartDate: startDate,
		EndDate:   endDate,
	}

	// 提取EntityId过滤器
	if entityId, ok := req.Filters["entityId"].(float64); ok {
		id := int64(entityId)
		analyticsReq.EntityId = &id
	}

	resp, err := s.analyticsService.GetOrderAnalytics(ctx, analyticsReq)
	if err != nil {
		return nil, err
	}

	return resp.StatusBreakdown, nil
}

// getRevenueMetrics 获取收入指标
func (s *OrderBIService) getRevenueMetrics(ctx context.Context, req *protocol.OrderMetricsReq) (interface{}, error) {
	startDate := req.StartDate
	endDate := req.EndDate
	if startDate == nil {
		t := time.Now().AddDate(0, -1, 0)
		startDate = &t
	}
	if endDate == nil {
		t := time.Now()
		endDate = &t
	}

	analyticsReq := &protocol.OrderAnalyticsReq{
		StartDate: startDate,
		EndDate:   endDate,
	}

	// 提取EntityId过滤器
	if entityId, ok := req.Filters["entityId"].(float64); ok {
		id := int64(entityId)
		analyticsReq.EntityId = &id
	}

	resp, err := s.analyticsService.GetOrderAnalytics(ctx, analyticsReq)
	if err != nil {
		return nil, err
	}

	return resp.RevenueAnalysis, nil
}
