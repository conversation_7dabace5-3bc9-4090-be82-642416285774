package service

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	"hotel/bi/domain"
	"hotel/bi/mysql"
	"hotel/bi/protocol"
	"hotel/common/datasource/database"
)

func TestOrderAnalyticsPipeline(t *testing.T) {
	// 这是一个集成测试，确保整个链路能够正常工作
	
	// 1. 初始化数据库连接（使用测试数据库）
	conn := database.Connect("item")
	orderDAO := mysql.NewOrderAnalyticsDAO(conn)
	
	// 2. 创建分析服务
	analyticsService := NewOrderAnalyticsService(orderDAO)
	biService := NewOrderBIService(analyticsService)
	
	ctx := context.Background()
	
	// 3. 测试实时指标获取
	t.Run("RealTimeMetrics", func(t *testing.T) {
		realTimeReq := &protocol.RealTimeMetricsReq{
			Metrics: []string{"todayOrders", "todayRevenue"},
		}
		
		resp, err := biService.GetRealTimeMetrics(ctx, realTimeReq)
		assert.NoError(t, err)
		assert.NotNil(t, resp)
		assert.GreaterOrEqual(t, resp.TodayOrders, int64(0))
	})
	
	// 4. 测试分析数据获取
	t.Run("OrderAnalytics", func(t *testing.T) {
		now := time.Now()
		startDate := now.AddDate(0, 0, -30) // 最近30天
		
		analyticsReq := &protocol.OrderAnalyticsReq{
			StartDate:   &startDate,
			EndDate:     &now,
			Granularity: "day",
		}
		
		resp, err := biService.GetOrderAnalytics(ctx, analyticsReq)
		assert.NoError(t, err)
		assert.NotNil(t, resp)
		assert.NotNil(t, resp.Overview)
	})
	
	// 5. 测试事件追踪
	t.Run("EventTracking", func(t *testing.T) {
		trackingDAO := mysql.NewLogDAO(conn)
		trackingService := NewTrackingService(trackingDAO)
		
		// 模拟搜索事件
		searchEvent := &domain.SearchTrackingEvent{
			SearchID:    "test_search_123",
			SessionID:   "test_session_123",
			QueryText:   "北京酒店",
			SearchType:  "city",
			Timestamp:   time.Now(),
			ResultCount: 10,
		}
		
		err := trackingService.TrackSearchEvent(ctx, searchEvent)
		assert.NoError(t, err)
	})
}

func TestRealTimeAnalyticsService(t *testing.T) {
	conn := database.Connect("item")
	orderDAO := mysql.NewOrderAnalyticsDAO(conn)
	
	realtimeService := NewRealTimeAnalyticsService(orderDAO)
	
	ctx := context.Background()
	
	t.Run("StartAndStop", func(t *testing.T) {
		// 启动服务
		err := realtimeService.Start(ctx)
		assert.NoError(t, err)
		assert.True(t, realtimeService.IsRunning())
		
		// 获取指标
		metrics := realtimeService.GetMetrics()
		assert.NotNil(t, metrics)
		
		// 停止服务
		realtimeService.Stop()
		assert.False(t, realtimeService.IsRunning())
	})
	
	t.Run("ForceUpdate", func(t *testing.T) {
		err := realtimeService.ForceUpdate(ctx)
		assert.NoError(t, err)
		
		metrics := realtimeService.GetMetrics()
		assert.NotNil(t, metrics)
		assert.False(t, metrics.UpdateTime.IsZero())
	})
}

func BenchmarkAnalyticsQuery(b *testing.B) {
	conn := database.Connect("item")
	orderDAO := mysql.NewOrderAnalyticsDAO(conn)
	analyticsService := NewOrderAnalyticsService(orderDAO)
	
	ctx := context.Background()
	now := time.Now()
	startDate := now.AddDate(0, 0, -7) // 最近7天
	
	req := &protocol.OrderAnalyticsReq{
		StartDate:   &startDate,
		EndDate:     &now,
		Granularity: "day",
	}
	
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := analyticsService.GetOrderAnalytics(ctx, req)
		if err != nil {
			b.Fatal(err)
		}
	}
}