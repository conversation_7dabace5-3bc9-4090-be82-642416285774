# Hotel BI 商业智能分析系统

## 📋 系统概述

Hotel BI订单分析系统是基于`bi/domain/BIOrder`设计的完整数据分析平台，实现了从搜索到预订再到业务洞察的全链路数据追踪和分析。系统严格遵循BIOrder域模型的设计理念，提供SmartBook、Rebook和客户分析三大核心功能模块。

本目录为BI（商业智能）相关服务，负责日志采集、分析、报表等功能。

## 🏗️ 目录结构

```
bi/
├── README.md                    # 本文档
├── docker-compose.yaml          # Docker部署配置
├── config/                      # 配置文件
│   ├── config.go               # 配置结构定义
│   └── config.yaml             # 默认配置
├── domain/                      # 领域模型
│   ├── order.go                # BIOrder核心模型
│   ├── tracking.go             # 事件追踪模型
│   └── log.go                  # 日志模型
├── mysql/                       # 数据访问层
│   ├── order_analytics_dao.go  # 订单分析DAO
│   ├── tracking_dao.go         # 事件追踪DAO
│   └── log.go                  # 日志DAO
├── protocol/                    # API协议定义
│   └── order_analytics.go      # 订单分析API协议
└── service/                     # 业务服务层
    ├── init.go                 # 服务初始化
    ├── order_analytics.go      # 订单分析服务
    ├── order_bi_api.go         # BI API服务
    ├── tracking_service.go     # 事件追踪服务
    ├── realtime_analytics.go   # 实时分析服务
    └── optimized_analytics.go  # 性能优化服务
```

## 🎯 核心功能模块

### 1. SmartBook Analytics (智能预订分析)

基于 BIOrder.Tags 字段实现，追踪智能预订优化效果：

- **ProfitGen 分析**: 统计利润生成优化的订单数量和金额
- **ErrorRecovery 分析**: 跟踪错误恢复处理的订单和节省金额
- **处理效率统计**: 总体处理的预订数量和金额分析

**API**: `GET /api/bi/getSmartBookOverview`

### 2. Rebook Analytics (重预订分析)

追踪重预订流程中的供应商转换和利润优化：

- **重预订统计**: 处理的重预订数量和金额
- **供应商转换追踪**: old_supplier → new_supplier 转换记录
- **利润优化分析**: 重预订过程中的利润生成统计

**API**: `GET /api/bi/getRebookOverview`

### 3. Client Analytics (客户分析)

基于 "Know your clients" 需求设计，全方位客户行为分析：

#### 配置分析
- 房型映射版本追踪
- SmartBook模式使用情况
- 动态加价和最佳套餐设置
- 双因素认证状态

#### 请求分析
- 搜索类型统计 (geo、hotel、keyword)
- API调用频率统计
- 可用性检查计数

#### 响应分析
- 成功率和失败率统计
- 性能指标 (平均、P50、P95响应时间)

**API**: `GET /api/bi/getClientAnalytics`

## 🔄 事件追踪系统

### 支持的事件类型

1. **SearchTrackingEvent** - 搜索事件追踪
2. **OrderCreationEvent** - 订单创建事件
3. **HotelViewEvent** - 酒店查看事件
4. **PriceCheckEvent** - 价格查询事件
5. **OrderStatusEvent** - 订单状态变更事件

### 集成服务

- **Search Service**: `search/service/hotel_list.go` - 搜索事件追踪
- **Trade Service**: `trade/service/order_create.go` - 订单事件追踪

## 📊 实时分析系统

### 实时指标
- 今日订单数和收入
- 活跃预订和待处理订单
- 完成率和取消率
- 自动更新（默认5分钟间隔）

### 性能优化
- 内存缓存策略（5-30分钟TTL）
- 并发安全的数据访问
- 定期清理过期缓存

## 🚀 部署环境要求

### Docker 部署
```bash
# 使用docker-compose启动BI服务
docker-compose -f ./bi/docker-compose.yaml up -d
```

**注意**: mac 本地可能跑不起来，建议使用Linux环境

### 开发及测试环境

| 模块 | CPU | 内存 | 磁盘 | 网络 | 实例数量（最低要求） |
|------|-----|------|------|------|-------------------|
| Frontend | 8 核 | 8 GB+ | SSD 或 SATA，10 GB+ | 千兆/万兆网卡 | 1 |
| Backend | 8 核 | 16 GB+ | SSD 或 SATA，50 GB+ | 千兆/万兆网卡 | 1 |

### 生产环境

| 模块 | CPU | 内存 | 磁盘 | 网络 | 实例数量（最低要求） |
|------|-----|------|------|------|-------------------|
| Frontend | 16 核 | 64 GB+ | SSD 或 RAID 卡，100GB+ | 万兆网卡 | 1 |
| Backend | 16 核 | 64 GB+ | SSD 或 SATA，100G+ | 万兆网卡 | 3 |

**部署注意事项**:
- 在验证测试环境中，可以将 FE 与 BE 部署在同一台服务器上
- 一台机器上一般只建议部署一个 BE 实例，同时只能部署一个 FE
- 如果需要 3 副本数据，那么至少需要 3 台机器各部署一个 BE 实例，而不是 1 台机器部署 3 个 BE 实例
- 多个 FE 所在服务器的时钟必须保持一致，最多允许 5 秒的时钟偏差
- 测试环境也可以仅使用一个 BE 进行测试。实际生产环境，BE 实例数量直接决定了整体查询延迟

### 快速启动

1. **配置数据库连接**
   ```yaml
   # bi/config/config.yaml
   DSN: "user:password@tcp(localhost:3306)/database?charset=utf8mb4&parseTime=True&loc=UTC"
   batchInterval: 1s
   ```

2. **初始化数据库表**
   ```sql
   -- 订单表 (基于BIOrder设计)
   CREATE TABLE `order` (
       `id` bigint PRIMARY KEY,
       `status` int NOT NULL,
       `tags` json,                    -- SmartBook标签
       `supplier` int NOT NULL,
       `create_time` datetime NOT NULL,
       `customer_entity_id` bigint,
       `profit_gen_amount` decimal(10,2),
       `buyer_amount` decimal(10,2),
       `old_supplier` varchar(50),     -- 重预订字段
       `new_supplier` varchar(50),
       `is_rebook` tinyint DEFAULT 0,
       INDEX `idx_create_time` (`create_time`),
       INDEX `idx_customer_entity` (`customer_entity_id`)
   );
   ```

3. **启动应用**
   ```bash
   make start-infra    # 启动MySQL、Redis
   make run-dev        # 启动应用服务
   ```

## 📡 API 接口

### 订单分析接口

```http
GET /api/bi/getOrderAnalytics
Content-Type: application/json

{
    "startDate": "2024-01-01T00:00:00Z",
    "endDate": "2024-01-31T23:59:59Z", 
    "granularity": "day",
    "entityId": 12345
}
```

### 实时指标接口

```http
GET /api/bi/getRealTimeMetrics
Content-Type: application/json

{
    "metrics": ["orders", "revenue", "completion_rate"]
}
```

## 📈 数据模型

### BIOrder 核心模型 (bi/domain/order.go)

```go
type BIOrder struct {
    ID               int64           `json:"id"`
    Status           int64           `json:"status"`
    Tags             []string        `json:"tags"`          // SmartBook标签
    Supplier         domain.Supplier `json:"supplier"`
    CreateTime       time.Time       `json:"create_time"`
    TenantEntityID   int64          `json:"tenant_entity_id"`
    CustomerEntityID int64          `json:"customer_entity_id"`
    ProfitGenAmount  float64        `json:"profit_gen_amount"`    // 利润生成金额
    ProfitGenCurrency float64       `json:"profit_gen_currency"`
}
```

## 🔍 监控和运维

### 关键监控指标

- **响应时间**: P50 < 100ms, P95 < 500ms
- **缓存命中率**: > 90%
- **数据库连接**: 活跃连接 < 50
- **错误率**: < 1%

### 健康检查

```bash
# 检查BI服务状态
curl http://localhost:8081/api/bi/name

# 检查实时指标
curl http://localhost:8081/api/bi/getRealTimeMetrics
```

## 📋 迭代开发约定

- 日志采集、分析需解耦，便于扩展
- 报表开发需关注性能与可视化
- 配置与代码分离，便于多环境部署

## ⚠️ 注意事项

- 严禁采集敏感/隐私数据
- 日志格式需标准化
- 变更需同步更新README

## 🚧 开发指南

### 添加新的分析功能

1. **定义数据模型** (bi/domain/)
2. **创建DAO方法** (bi/mysql/)
3. **实现业务逻辑** (bi/service/)
4. **定义API协议** (bi/protocol/)
5. **注册API接口** (bi/service/order_bi_api.go)

### 测试

```bash
# 运行单元测试
go test ./bi/...

# 运行集成测试
make test

# 性能测试
go test -bench=. ./bi/service/
```

## 📋 待办事项

### 高优先级
- [ ] 创建数据库表结构
- [ ] 完善集成测试
- [ ] 性能基准测试

### 中优先级  
- [ ] 机器学习预测集成
- [ ] 实时流处理优化
- [ ] 自定义仪表板

### 低优先级
- [ ] 多租户数据隔离
- [ ] 历史数据归档策略
- [ ] AI驱动的异常检测

## 📚 相关文档

- [BIOrder 设计文档](domain/order.go) - 查看详细的领域模型设计
- [API 接口文档](protocol/order_analytics.go) - 完整的API定义
- [部署指南](../README.md) - 整体项目部署说明

---

**最后更新**: 2025-07-27  
**维护者**: Hotel BI Team  
**版本**: v1.0.0