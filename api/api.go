package main

import (
	"flag"
	"net/http"
	"time"

	"hotel/common/envhelper"
	"hotel/common/log"
	tradeSrv "hotel/trade/service"
	userSrv "hotel/user/service"

	"github.com/zeromicro/go-zero/rest"

	"hotel/api/config"
	"hotel/api/middleware"
	"hotel/api/service"

	httpUtil "hotel/common/httpdispatcher"
)

func main() {
	st := time.Now()
	log.Info("start env(%s) service(%s)", envhelper.GetENV(), envhelper.GetServiceName())
	flag.Parse()

	c := config.Init()

	initInfra()
	server := rest.MustNewServer(c.RestConf)
	defer server.Stop()

	server.Use(middleware.NewCorsMiddleware().Handle)

	ctx := service.NewServiceContext(c)
	customizedRouter(server, ctx)
	log.Info("Starting server at %s:%d, cost:%s...\n", c.Host, c.Port, time.Since(st))
	server.Start()
}

func initInfra() {
}

func customizedRouter(server *rest.Server, serverCtx *service.Dependency) {
	srvDispatcher, err := httpUtil.NewServiceDispatcherBuilder().WithJwt(serverCtx.JwtAuth).Build()
	if err != nil {
		panic(err)
	}

	srvDispatcher.RegisterService(&userSrv.InternalService{UserService: serverCtx.UserSrv}) // 内网接口

	srvDispatcher.RegisterService(&userSrv.PlatformService{UserService: serverCtx.UserSrv}) // 平台接口（admin portal)
	srvDispatcher.RegisterService(&userSrv.TenantService{UserService: serverCtx.UserSrv})   // 租户接口
	srvDispatcher.RegisterService(&userSrv.CustomerService{UserService: serverCtx.UserSrv})
	srvDispatcher.RegisterService(&userSrv.ExtranetService{UserService: serverCtx.UserSrv})
	srvDispatcher.RegisterService(serverCtx.UserSrv)
	srvDispatcher.RegisterService(serverCtx.RuleSrv)
	srvDispatcher.RegisterService(serverCtx.TradeSrv)
	srvDispatcher.RegisterService(&tradeSrv.CustomerService{TradeService: serverCtx.TradeSrv})
	srvDispatcher.RegisterService(&tradeSrv.TenantService{TradeService: serverCtx.TradeSrv})
	srvDispatcher.RegisterService(serverCtx.AuthSrv)
	srvDispatcher.RegisterService(serverCtx.SearchSrv)
	srvDispatcher.RegisterService(serverCtx.FunSrv)
	srvDispatcher.RegisterService(serverCtx.ViewSrv)
	srvDispatcher.RegisterService(serverCtx.ContentSrv)
	srvDispatcher.RegisterService(serverCtx.StarlingSrv)
	srvDispatcher.RegisterService(serverCtx.QuestionnaireSrv)
	srvDispatcher.RegisterService(serverCtx.LogViewerSrv)
	srvDispatcher.RegisterService(serverCtx.BISrv)

	apiGroup := []rest.Route{
		{Method: http.MethodPost, Path: "/api/:domain/:service/:method", Handler: srvDispatcher.Dispatch},
		{Method: http.MethodOptions, Path: "/api/:domain/:service/:method", Handler: srvDispatcher.Dispatch},
		{Method: http.MethodGet, Path: "/api/:domain/:service/:method", Handler: srvDispatcher.Dispatch},
		{Method: http.MethodPost, Path: "/api/:service/:method", Handler: srvDispatcher.Dispatch},
		{Method: http.MethodOptions, Path: "/api/:service/:method", Handler: srvDispatcher.Dispatch},
		{Method: http.MethodGet, Path: "/api/:service/:method", Handler: srvDispatcher.Dispatch},
	}
	server.AddRoutes(apiGroup)
	// 如果有专属客户/版本 API，另外注册一个apiGroup ...
}
