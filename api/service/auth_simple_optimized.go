package service

import (
	"context"
	"errors"
	"time"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/stores/redis"
	"golang.org/x/crypto/bcrypt"

	"hotel/api/config"
	apiProto "hotel/api/protocol"
	httpUtil "hotel/common/httpdispatcher"
	"hotel/common/types"
	"hotel/user/cache"
	"hotel/user/domain"
	userProto "hotel/user/protocol"
	userSrv "hotel/user/service"
)

// SimpleOptimizedAuthService 简化版优化认证服务
type SimpleOptimizedAuthService struct {
	userSrv   *userSrv.UserService
	jwt       *httpUtil.JwtPlugin
	userCache cache.UserCache
	rds       *redis.Redis
}

// NewSimpleOptimizedAuthService 创建简化版优化认证服务
func NewSimpleOptimizedAuthService(
	jwt *httpUtil.JwtPlugin,
	userSrv *userSrv.UserService,
	c config.Config,
) *SimpleOptimizedAuthService {
	// 初始化Redis连接
	rds := redis.MustNewRedis(c.Redis)

	return &SimpleOptimizedAuthService{
		jwt:       jwt,
		userSrv:   userSrv,
		userCache: cache.NewUserCache(*rds),
		rds:       rds,
	}
}

func (s *SimpleOptimizedAuthService) Name() string {
	return "auth_simple_optimized"
}

// LoginWithCache 带缓存的登录方法
// @tags: internal.hotelbyte.com
// @auth: false
func (s *SimpleOptimizedAuthService) LoginWithCache(ctx context.Context, req *apiProto.LoginReq) (*apiProto.LoginResp, error) {
	startTime := time.Now()

	// 1. 首先尝试从缓存获取用户基本信息
	userBasic, err := s.getUserBasicWithCache(ctx, req.Email)
	if err != nil {
		logx.WithContext(ctx).Errorf("failed to get user basic info: %v", err)
		return nil, err
	}

	// 2. 验证密码
	if err = bcrypt.CompareHashAndPassword([]byte(userBasic.Secret), []byte(req.Password)); err != nil {
		logx.WithContext(ctx).Infof("password verify failed for user %s: %v", req.Email, err)
		return nil, domain.ErrAuthentication
	}

	// 3. 获取完整用户信息（包括角色和权限）
	user, err := s.getUserWithCache(ctx, userBasic.ID)
	if err != nil {
		logx.WithContext(ctx).Errorf("failed to get full user info: %v", err)
		return nil, err
	}

	// 4. 生成JWT token
	jwtToken, err := s.jwt.Generate(user, req.TTL)
	if err != nil {
		logx.WithContext(ctx).Errorf("failed to generate JWT token: %v", err)
		return nil, err
	}

	// 5. 缓存用户会话信息
	if err = s.userCache.SetUserSession(ctx, jwtToken, user); err != nil {
		logx.WithContext(ctx).Errorf("failed to cache user session: %v", err)
		// 不中断登录流程，只记录错误
	}

	// 记录性能指标
	duration := time.Since(startTime)
	logx.WithContext(ctx).Infof("login completed for user %s in %v", req.Email, duration)

	return &apiProto.LoginResp{Token: jwtToken, User: user}, nil
}

// getUserBasicWithCache 带缓存的获取用户基本信息
func (s *SimpleOptimizedAuthService) getUserBasicWithCache(ctx context.Context, key string) (*domain.UserBasic, error) {
	// 1. 尝试从缓存获取
	userBasic, err := s.userCache.GetUserBasic(ctx, key)
	if err == nil {
		return userBasic, nil
	}

	// 2. 缓存未命中，从数据库获取
	if err == redis.Nil {
		// 硬编码的超级管理员检查
		cfg := s.jwt.GetConfig()
		if key == cfg.Issuer {
			return domain.GetSuperAdminUser(cfg.Issuer, cfg.Secret).UserBasic, nil
		}

		// 使用优化后的用户服务
		userBasic, err = s.userSrv.GetUserBasicOptimized(ctx, key)
		if err != nil {
			return nil, err
		}

		// 3. 设置缓存
		if cacheErr := s.userCache.SetUserBasic(ctx, key, userBasic); cacheErr != nil {
			logx.WithContext(ctx).Errorf("failed to cache user basic info: %v", cacheErr)
		}

		return userBasic, nil
	}

	// 其他错误
	return nil, err
}

// getUserWithCache 带缓存的获取完整用户信息
func (s *SimpleOptimizedAuthService) getUserWithCache(ctx context.Context, userID types.ID) (*domain.User, error) {
	// 1. 尝试从缓存获取
	user, err := s.userCache.GetUser(ctx, userID)
	if err == nil {
		return user, nil
	}

	// 2. 缓存未命中，从数据库获取
	if err == redis.Nil {
		// 使用优化后的用户服务
		userResp, err := s.userSrv.GetUserOptimized(ctx, &userProto.GetUserReq{Key: userID.String()})
		if err != nil {
			return nil, err
		}

		user = userResp.User

		// 3. 设置缓存
		if cacheErr := s.userCache.SetUser(ctx, userID, user); cacheErr != nil {
			logx.WithContext(ctx).Errorf("failed to cache user info: %v", cacheErr)
		}

		return user, nil
	}

	// 其他错误
	return nil, err
}

// TicketWithCache 带缓存的获取API票据方法
// @desc: Get your api access ticket by your apiKey and apiSecret with caching
// @tags: openapi
// @auth: false
func (s *SimpleOptimizedAuthService) TicketWithCache(ctx context.Context, req *apiProto.TicketReq) (*apiProto.TicketResp, error) {
	resp, err := s.LoginWithCache(ctx, &apiProto.LoginReq{
		Email:    req.AppKey,
		Password: req.AppSecret,
		TTL:      req.TTL,
	})
	if err != nil {
		return nil, err
	}
	return &apiProto.TicketResp{
		Ticket: resp.Token,
	}, nil
}

// ValidateTokenWithCache 带缓存的token验证方法
func (s *SimpleOptimizedAuthService) ValidateTokenWithCache(ctx context.Context, token string) (*domain.User, error) {
	// 1. 尝试从会话缓存获取
	user, err := s.userCache.GetUserSession(ctx, token)
	if err == nil {
		return user, nil
	}

	// 2. 缓存未命中，解析JWT token
	if err == redis.Nil {
		claims, err := s.jwt.Parse(token)
		if err != nil {
			return nil, err
		}

		if claims.LoginUser == nil {
			return nil, domain.ErrAuthentication
		}

		// 3. 重新缓存会话信息
		if cacheErr := s.userCache.SetUserSession(ctx, token, claims.LoginUser); cacheErr != nil {
			logx.WithContext(ctx).Errorf("failed to cache user session: %v", cacheErr)
		}

		return claims.LoginUser, nil
	}

	// 其他错误
	return nil, err
}

// RefreshUserCache 刷新用户缓存
func (s *SimpleOptimizedAuthService) RefreshUserCache(ctx context.Context, userID types.ID) error {
	// 1. 删除现有缓存
	if err := s.userCache.DelUser(ctx, userID); err != nil {
		logx.WithContext(ctx).Errorf("failed to delete user cache: %v", err)
	}

	// 2. 重新加载用户信息
	userResp, err := s.userSrv.GetUserOptimized(ctx, &userProto.GetUserReq{Key: userID.String()})
	if err != nil {
		return err
	}

	// 3. 设置新缓存
	return s.userCache.SetUser(ctx, userID, userResp.User)
}

// GetCacheStats 获取缓存统计信息
func (s *SimpleOptimizedAuthService) GetCacheStats(ctx context.Context) (map[string]interface{}, error) {
	// 简单的缓存统计
	return map[string]interface{}{
		"redis_connected": true,
		"cache_enabled":   true,
	}, nil
}

// HealthCheck 健康检查
func (s *SimpleOptimizedAuthService) HealthCheck(ctx context.Context) error {
	// 检查Redis连接
	if !s.rds.Ping() {
		return errors.New("redis connection failed")
	}
	return nil
}
