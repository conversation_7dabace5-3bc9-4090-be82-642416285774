package service

import (
	"context"
	"time"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/stores/redis"
	"golang.org/x/crypto/bcrypt"

	apiProto "hotel/api/protocol"
	"hotel/common/types"
	httpUtil "hotel/common/httpdispatcher"
	"hotel/user/cache"
	"hotel/user/domain"
	"hotel/user/mysql"
	userSrv "hotel/user/service"
)

// OptimizedAuthService 优化后的认证服务
type OptimizedAuthService struct {
	userSrv     *userSrv.UserService
	jwt         *httpUtil.JwtPlugin
	userCache   cache.UserCache
	optimizedDao *mysql.OptimizedUserDao
}

// NewOptimizedAuthService 创建优化后的认证服务
func NewOptimizedAuthService(
	jwt *httpUtil.JwtPlugin, 
	userSrv *userSrv.UserService,
	rds redis.Redis,
	optimizedDao *mysql.OptimizedUserDao,
) *OptimizedAuthService {
	return &OptimizedAuthService{
		jwt:         jwt,
		userSrv:     userSrv,
		userCache:   cache.NewUserCache(rds),
		optimizedDao: optimizedDao,
	}
}

func (s *OptimizedAuthService) Name() string {
	return "auth_optimized"
}

// LoginOptimized 优化后的登录方法
// @tags: internal.hotelbyte.com
// @auth: false
func (s *OptimizedAuthService) LoginOptimized(ctx context.Context, req *apiProto.LoginReq) (*apiProto.LoginResp, error) {
	startTime := time.Now()
	
	// 1. 首先尝试从缓存获取用户基本信息
	userBasic, err := s.getUserBasicWithCache(ctx, req.Email)
	if err != nil {
		logx.WithContext(ctx).Errorf("failed to get user basic info: %v", err)
		return nil, err
	}
	
	// 2. 验证密码
	if err = s.verifyPassword(ctx, userBasic, req.Password); err != nil {
		logx.WithContext(ctx).Infof("password verify failed for user %s: %v", req.Email, err)
		return nil, domain.ErrAuthentication
	}
	
	// 3. 获取完整用户信息（包括角色和权限）
	user, err := s.getUserWithCache(ctx, userBasic.ID)
	if err != nil {
		logx.WithContext(ctx).Errorf("failed to get full user info: %v", err)
		return nil, err
	}
	
	// 4. 生成JWT token
	jwtToken, err := s.jwt.Generate(user, req.TTL)
	if err != nil {
		logx.WithContext(ctx).Errorf("failed to generate JWT token: %v", err)
		return nil, err
	}
	
	// 5. 缓存用户会话信息
	if err = s.userCache.SetUserSession(ctx, jwtToken, user); err != nil {
		logx.WithContext(ctx).Errorf("failed to cache user session: %v", err)
		// 不中断登录流程，只记录错误
	}
	
	// 记录性能指标
	duration := time.Since(startTime)
	logx.WithContext(ctx).Infof("login completed for user %s in %v", req.Email, duration)
	
	return &apiProto.LoginResp{Token: jwtToken, User: user}, nil
}

// getUserBasicWithCache 带缓存的获取用户基本信息
func (s *OptimizedAuthService) getUserBasicWithCache(ctx context.Context, key string) (*domain.UserBasic, error) {
	// 1. 尝试从缓存获取
	userBasic, err := s.userCache.GetUserBasic(ctx, key)
	if err == nil {
		return userBasic, nil
	}
	
	// 2. 缓存未命中，从数据库获取
	if err == redis.Nil {
		// 硬编码的超级管理员检查
		cfg := s.jwt.GetConfig()
		if key == cfg.Issuer {
			return domain.GetSuperAdminUser(cfg.Issuer, cfg.Secret).UserBasic, nil
		}
		
		// 使用优化后的查询
		userBasic, err = s.optimizedDao.GetUserBasicByKey(ctx, key)
		if err != nil {
			return nil, err
		}
		
		// 3. 设置缓存
		if cacheErr := s.userCache.SetUserBasic(ctx, key, userBasic); cacheErr != nil {
			logx.WithContext(ctx).Errorf("failed to cache user basic info: %v", cacheErr)
		}
		
		return userBasic, nil
	}
	
	// 其他错误
	return nil, err
}

// getUserWithCache 带缓存的获取完整用户信息
func (s *OptimizedAuthService) getUserWithCache(ctx context.Context, userID types.ID) (*domain.User, error) {
	// 1. 尝试从缓存获取
	user, err := s.userCache.GetUser(ctx, userID)
	if err == nil {
		return user, nil
	}
	
	// 2. 缓存未命中，从数据库获取
	if err == redis.Nil {
		// 使用优化后的查询
		user, err = s.optimizedDao.GetUserOptimized(ctx, userID.Int64())
		if err != nil {
			return nil, err
		}
		
		// 3. 设置缓存
		if cacheErr := s.userCache.SetUser(ctx, userID, user); cacheErr != nil {
			logx.WithContext(ctx).Errorf("failed to cache user info: %v", cacheErr)
		}
		
		return user, nil
	}
	
	// 其他错误
	return nil, err
}

// verifyPassword 验证密码
func (s *OptimizedAuthService) verifyPassword(ctx context.Context, user *domain.UserBasic, password string) error {
	return bcrypt.CompareHashAndPassword([]byte(user.Secret), []byte(password))
}

// TicketOptimized 优化后的获取API票据方法
// @desc: Get your api access ticket by your apiKey and apiSecret. You're suggested to use caching tools to cache the ticket to prevent calling Ticket before every call. Since the returned ticket will be expired after TTL you specified in request (default as 1 hour) . If you have no apiKey or apiSecret, please contact your tenant team.
// @tags: openapi
// @auth: false
func (s *OptimizedAuthService) TicketOptimized(ctx context.Context, req *apiProto.TicketReq) (*apiProto.TicketResp, error) {
	resp, err := s.LoginOptimized(ctx, &apiProto.LoginReq{
		Email:    req.AppKey,
		Password: req.AppSecret,
		TTL:      req.TTL,
	})
	if err != nil {
		return nil, err
	}
	return &apiProto.TicketResp{
		Ticket: resp.Token,
	}, nil
}

// ValidateTokenOptimized 优化后的token验证方法
func (s *OptimizedAuthService) ValidateTokenOptimized(ctx context.Context, token string) (*domain.User, error) {
	// 1. 尝试从会话缓存获取
	user, err := s.userCache.GetUserSession(ctx, token)
	if err == nil {
		return user, nil
	}
	
	// 2. 缓存未命中，解析JWT token
	if err == redis.Nil {
		claims, err := s.jwt.Parse(token)
		if err != nil {
			return nil, err
		}
		
		if claims.LoginUser == nil {
			return nil, domain.ErrAuthentication
		}
		
		// 3. 重新缓存会话信息
		if cacheErr := s.userCache.SetUserSession(ctx, token, claims.LoginUser); cacheErr != nil {
			logx.WithContext(ctx).Errorf("failed to cache user session: %v", cacheErr)
		}
		
		return claims.LoginUser, nil
	}
	
	// 其他错误
	return nil, err
}

// RefreshUserCache 刷新用户缓存
func (s *OptimizedAuthService) RefreshUserCache(ctx context.Context, userID types.ID) error {
	// 1. 删除现有缓存
	if err := s.userCache.DelUser(ctx, userID); err != nil {
		logx.WithContext(ctx).Errorf("failed to delete user cache: %v", err)
	}
	
	// 2. 重新加载用户信息
	user, err := s.optimizedDao.GetUserOptimized(ctx, userID.Int64())
	if err != nil {
		return err
	}
	
	// 3. 设置新缓存
	return s.userCache.SetUser(ctx, userID, user)
}

// InvalidateUserSessions 使用户所有会话失效
func (s *OptimizedAuthService) InvalidateUserSessions(ctx context.Context, userID types.ID) error {
	// 这里需要实现会话管理，可能需要维护用户的活跃token列表
	// 当前简化实现：删除用户相关缓存
	return s.userCache.DelUserRelatedCache(ctx, userID)
}

// GetLoginStats 获取登录统计信息
func (s *OptimizedAuthService) GetLoginStats(ctx context.Context) (*LoginStats, error) {
	// 这里可以实现登录统计逻辑
	return &LoginStats{
		TotalLogins:    0,
		SuccessLogins:  0,
		FailedLogins:   0,
		CacheHitRate:   0,
		AvgResponseTime: 0,
	}, nil
}

// LoginStats 登录统计信息
type LoginStats struct {
	TotalLogins     int64   `json:"total_logins"`
	SuccessLogins   int64   `json:"success_logins"`
	FailedLogins    int64   `json:"failed_logins"`
	CacheHitRate    float64 `json:"cache_hit_rate"`
	AvgResponseTime int64   `json:"avg_response_time_ms"`
}

// BatchValidateTokens 批量验证tokens
func (s *OptimizedAuthService) BatchValidateTokens(ctx context.Context, tokens []string) (map[string]*domain.User, error) {
	result := make(map[string]*domain.User)
	
	for _, token := range tokens {
		user, err := s.ValidateTokenOptimized(ctx, token)
		if err == nil {
			result[token] = user
		}
	}
	
	return result, nil
}

// PreloadUserCache 预加载用户缓存（用于系统启动时的缓存预热）
func (s *OptimizedAuthService) PreloadUserCache(ctx context.Context, userIDs []types.ID) error {
	for _, userID := range userIDs {
		user, err := s.optimizedDao.GetUserOptimized(ctx, userID.Int64())
		if err != nil {
			logx.WithContext(ctx).Errorf("failed to preload user %v: %v", userID, err)
			continue
		}
		
		if err := s.userCache.SetUser(ctx, userID, user); err != nil {
			logx.WithContext(ctx).Errorf("failed to cache preloaded user %v: %v", userID, err)
		}
	}
	
	return nil
}

// HealthCheck 健康检查
func (s *OptimizedAuthService) HealthCheck(ctx context.Context) error {
	// 检查缓存连接
	_, err := s.userCache.GetUser(ctx, types.ID(0))
	if err != nil && err != redis.Nil {
		return err
	}
	
	// 检查数据库连接
	_, err = s.optimizedDao.GetUserBasicByKey(ctx, "health_check_dummy")
	if err != nil && err != mysql.ErrNotFound {
		return err
	}
	
	return nil
}
