# 订单取消功能部署指南

## 概述

本文档描述了订单取消功能的部署准备工作，包括配置项设置、数据库迁移、消息队列配置和生产环境监控设置。

## 1. 配置项检查

### 1.1 CQRS 消息队列配置

确保以下配置项在生产环境中正确设置：

```yaml
# config/production.yaml
cqrs:
  type: "redis_stream"
  redis:
    host: "${REDIS_HOST}"
    port: ${REDIS_PORT}
    password: "${REDIS_PASSWORD}"
    db: ${REDIS_DB}
    pool_size: 10
    max_retries: 3
    dial_timeout: 5s
    read_timeout: 3s
    write_timeout: 3s

# 取消消息队列配置
cancel_queue:
  topic: "order_cancel"
  channel: "cancel_consumer"
  consumer_name: "cancel_consumer_1"
  max_in_flight: 10
  max_attempts: 3
  retry_delay: 2s
  dead_letter_topic: "order_cancel_dead_letter"
```

### 1.2 供应商配置

确保所有供应商的取消接口配置正确：

```yaml
suppliers:
  tbo:
    cancel_endpoint: "${TBO_CANCEL_ENDPOINT}"
    timeout: 30s
    max_retries: 3
  trip:
    cancel_endpoint: "${TRIP_CANCEL_ENDPOINT}"
    timeout: 30s
    max_retries: 3
  dida:
    cancel_endpoint: "${DIDA_CANCEL_ENDPOINT}"
    timeout: 30s
    max_retries: 3
  simulator:
    cancel_endpoint: "${SIMULATOR_CANCEL_ENDPOINT}"
    timeout: 10s
    max_retries: 1
```

### 1.3 审计日志配置

```yaml
audit:
  enabled: true
  log_level: "info"
  output_format: "json"
  destinations:
    - type: "file"
      path: "/var/log/hotel/audit.log"
      max_size: "100MB"
      max_backups: 10
    - type: "elasticsearch"
      endpoint: "${ELASTICSEARCH_ENDPOINT}"
      index: "hotel-audit"
```

## 2. 数据库迁移

### 2.1 审计日志表创建

```sql
-- 创建订单取消审计日志表
CREATE TABLE IF NOT EXISTS order_cancel_audit (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    order_id BIGINT NOT NULL,
    action VARCHAR(50) NOT NULL,
    user_id BIGINT,
    original_status INT NOT NULL,
    final_status INT NOT NULL,
    supplier_cancel_success BOOLEAN DEFAULT FALSE,
    supplier_errors JSON,
    reason TEXT,
    metadata JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_order_id (order_id),
    INDEX idx_action (action),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 创建取消操作监控指标表
CREATE TABLE IF NOT EXISTS cancel_metrics (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    date DATE NOT NULL,
    total_requests BIGINT DEFAULT 0,
    successful_cancellations BIGINT DEFAULT 0,
    failed_cancellations BIGINT DEFAULT 0,
    supplier_failures BIGINT DEFAULT 0,
    database_failures BIGINT DEFAULT 0,
    message_queue_failures BIGINT DEFAULT 0,
    avg_processing_time_ms BIGINT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uk_date (date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

### 2.2 索引优化

```sql
-- 为订单表添加状态索引（如果不存在）
CREATE INDEX IF NOT EXISTS idx_orders_status ON orders (status);

-- 为供应商订单表添加订单ID索引（如果不存在）
CREATE INDEX IF NOT EXISTS idx_supplier_orders_order_id ON supplier_orders (order_id);

-- 为订单表添加更新时间索引
CREATE INDEX IF NOT EXISTS idx_orders_update_time ON orders (update_time);
```

## 3. Redis Stream 配置

### 3.1 创建消息流

```bash
# 连接到 Redis
redis-cli -h ${REDIS_HOST} -p ${REDIS_PORT} -a ${REDIS_PASSWORD}

# 创建取消订单消息流
XGROUP CREATE order_cancel cancel_consumer $ MKSTREAM

# 创建死信队列流
XGROUP CREATE order_cancel_dead_letter dead_letter_consumer $ MKSTREAM
```

### 3.2 监控 Redis Stream

```bash
# 检查消息流状态
XINFO STREAM order_cancel

# 检查消费者组状态
XINFO GROUPS order_cancel

# 检查待处理消息
XPENDING order_cancel cancel_consumer
```

## 4. 监控和告警设置

### 4.1 Prometheus 指标

在应用中暴露以下指标：

```go
// 取消请求总数
cancel_requests_total{status="success|failure"}

// 取消处理时间
cancel_processing_duration_seconds

// 供应商取消失败数
supplier_cancel_failures_total{supplier="tbo|trip|dida|simulator"}

// 消息队列处理延迟
cancel_queue_processing_lag_seconds

// 数据库操作失败数
cancel_database_failures_total{operation="update|select"}
```

### 4.2 告警规则

```yaml
# prometheus/alerts/cancel-orders.yml
groups:
  - name: cancel_orders
    rules:
      - alert: HighCancelFailureRate
        expr: rate(cancel_requests_total{status="failure"}[5m]) / rate(cancel_requests_total[5m]) > 0.05
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "订单取消失败率过高"
          description: "过去5分钟内订单取消失败率超过5%"

      - alert: CancelProcessingTooSlow
        expr: histogram_quantile(0.95, rate(cancel_processing_duration_seconds_bucket[5m])) > 5
        for: 3m
        labels:
          severity: warning
        annotations:
          summary: "订单取消处理过慢"
          description: "95%的取消请求处理时间超过5秒"

      - alert: SupplierCancelFailures
        expr: rate(supplier_cancel_failures_total[5m]) > 0.1
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "供应商取消接口异常"
          description: "供应商取消接口失败率过高"

      - alert: CancelQueueLag
        expr: cancel_queue_processing_lag_seconds > 30
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "取消消息队列处理延迟"
          description: "取消消息队列处理延迟超过30秒"
```

### 4.3 Grafana 仪表板

创建包含以下面板的仪表板：

1. 取消请求总数和成功率
2. 取消处理时间分布
3. 供应商取消成功率
4. 消息队列处理状态
5. 数据库操作性能
6. 错误日志统计

## 5. 部署检查清单

### 5.1 部署前检查

- [ ] 所有配置项已正确设置
- [ ] 数据库迁移已执行
- [ ] Redis Stream 已创建
- [ ] 供应商接口连通性测试通过
- [ ] 单元测试和集成测试通过
- [ ] 端到端测试通过
- [ ] 性能测试通过
- [ ] 安全扫描通过

### 5.2 部署后验证

- [ ] 应用启动成功
- [ ] CQRS 消费者正常运行
- [ ] 监控指标正常上报
- [ ] 告警规则生效
- [ ] 日志输出正常
- [ ] 取消功能端到端测试通过

### 5.3 回滚计划

如果部署出现问题，按以下步骤回滚：

1. 停止新版本应用
2. 启动旧版本应用
3. 恢复旧版本配置
4. 清理未处理的消息队列消息
5. 验证功能正常

## 6. 生产环境监控

### 6.1 关键指标监控

- 取消成功率 > 95%
- 平均处理时间 < 3秒
- 供应商接口成功率 > 98%
- 消息队列延迟 < 10秒

### 6.2 日志监控

监控以下关键日志：

- 取消请求日志
- 供应商接口调用日志
- 数据库操作日志
- 错误和异常日志

### 6.3 业务监控

- 每日取消订单数量
- 取消原因分布
- 退款处理时间
- 客户投诉率

## 7. 故障处理

### 7.1 常见问题

1. **消息队列堆积**
   - 检查消费者状态
   - 增加消费者实例
   - 检查处理逻辑性能

2. **供应商接口超时**
   - 检查网络连接
   - 调整超时配置
   - 联系供应商技术支持

3. **数据库锁等待**
   - 检查事务大小
   - 优化SQL查询
   - 调整数据库连接池

### 7.2 应急处理

如果出现严重问题：

1. 立即停止取消功能
2. 切换到手动处理模式
3. 通知相关团队
4. 分析问题原因
5. 制定修复方案

## 8. 性能优化建议

1. **数据库优化**
   - 使用读写分离
   - 添加适当索引
   - 定期清理历史数据

2. **缓存优化**
   - 缓存订单状态
   - 缓存供应商配置
   - 使用分布式缓存

3. **消息队列优化**
   - 调整批处理大小
   - 使用消息分区
   - 监控队列深度

4. **代码优化**
   - 异步处理非关键操作
   - 减少数据库查询次数
   - 使用连接池

## 9. 部署脚本

参考 `deploy/deploy-cancel-feature.sh` 脚本进行自动化部署。
