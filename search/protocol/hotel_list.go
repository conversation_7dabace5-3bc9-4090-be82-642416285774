package protocol

import (
	"hotel/common/pagehelper"
	commonProto "hotel/common/protocol"
	"hotel/common/types"
	"hotel/content/domain"
	geoDomain "hotel/geography/domain"
	supplierDomain "hotel/supplier/domain"
	userDomain "hotel/user/domain"
)

type HotelListReq struct {
	Operator     *userDomain.User `json:"operator" apidoc:"-"`
	HotelIds     types.IDs        `json:"hotelIds"`
	RequireRooms bool             `json:"requireRooms"` // if requireRooms is true, the QPS restriction would be taken as same as HotelRates
	supplierDomain.CheckInOut
	supplierDomain.GuestRoomOption
	supplierDomain.CurrencyOption
	HotelRegion
	HotelListFilter
	pagehelper.PageReq
	commonProto.Header
}

type HotelRegion struct {
	// optional. If regionName is not set, regionCode is required.
	RegionId types.ID `json:"regionId"`

	// optional. If regionCode is not set, regionName is required. The system will automatically search cities with the regionName.
	RegionName string `json:"regionName"`
}

type HotelListFilter struct {
	Distance          *supplierDomain.DistanceFilter `json:"distance,omitempty"`                             // distance filter
	Price             *supplierDomain.PriceFilter    `json:"price,omitempty"`                                // price filter
	InternalSuppliers []supplierDomain.Supplier      `json:"internalSuppliers,omitempty" apidoc:"HotelCode"` // supplier filter for internal platform

	// Hotels of room type id to filter by. You can get all the possible room type code values through the room types operation of [Hotel Content API.
	RoomTypeIds string `json:"roomTypeIds,omitempty"`
	
	// Additional search filters
	HotelName   string                        `json:"hotelName,omitempty"`   // Filter by hotel name
	Rating      *int                          `json:"rating,omitempty"`      // Filter by minimum rating (1-5)
	SortBy      string                        `json:"sortBy,omitempty"`      // Sort order: price-asc, price-desc, rating-desc, distance-asc
	MinPrice    *float64                      `json:"minPrice,omitempty"`    // Minimum price filter
	MaxPrice    *float64                      `json:"maxPrice,omitempty"`    // Maximum price filter
	Amenities   []string                      `json:"amenities,omitempty"`   // Filter by amenities
}

type HotelListResp struct {
	List     domain.HotelList         `json:"list,omitzero"`
	Basic    HotelListBasicInfo       `json:"basic,omitzero"`
	Metadata map[string]interface{}   `json:"metadata,omitempty"` // 追踪元数据
	*pagehelper.PageResp
}

type HotelListBasicInfo struct {
	Region geoDomain.Region `json:"region"`
}
