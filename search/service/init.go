package service

import (
	"github.com/zeromicro/go-zero/core/conf"
	"github.com/zeromicro/go-zero/core/stores/redis"

	confutil "hotel/common/config"
	commonService "hotel/common/service"
	contentSrv "hotel/content/service"
	geoSrv "hotel/geography/service"
	"hotel/mapping/supplier/giata"
	"hotel/mapping/supplier/olivier"
	ruleSrv "hotel/rule/service"
	"hotel/search/config"
	"hotel/supplier"
	supplierDomain "hotel/supplier/domain"
	userSrv "hotel/user/service"
)

var configFile = confutil.SafeFlagString("search", "search/config/config.yaml", "the search config file")

type SearchService struct {
	geoSrv             *geoSrv.GeographyService
	contentSrv         *contentSrv.ContentService
	userSrv            *userSrv.UserService
	ruleSrv            *ruleSrv.RuleService
	supplierFact       *supplier.Factory
	cache              *redis.Redis
	olivierClient      *olivier.OlivierClient       // Renamed from roomMatchCli
	giataRoomClient    *giata.GiataRoomMappingClient // New GIATA mapping client
	availableSuppliers []supplierDomain.Supplier
	priceCacheSrv      *HotelPriceCacheService // 价格缓存服务
	sessionService     *commonService.SessionService
	// 移除trackingService，直接基于BIOrder记录关键指标
}

func (s *SearchService) Name() string {
	return "search"
}

func NewSearchService() *SearchService {
	var cfg config.Config
	conf.MustLoad(*configFile, &cfg)
	r, err := redis.NewRedis(cfg.Redis)
	if err != nil {
		panic(err)
	}
	supplierFactory := supplier.NewFactory()

	return &SearchService{
		geoSrv:             geoSrv.NewGeographyService(),
		contentSrv:         contentSrv.NewContentService(),
		userSrv:            userSrv.NewUserService(),
		ruleSrv:            ruleSrv.NewRuleService(),
		supplierFact:       supplierFactory,
		cache:              r,
		olivierClient:      olivier.NewOlivierClient(),
		giataRoomClient:    giata.NewGiataRoomMappingClient(),
		availableSuppliers: []supplierDomain.Supplier{
			supplierDomain.Supplier_Dida,
			supplierDomain.Supplier_Trip,        // Ctrip supplier
			supplierDomain.Supplier_Derbysoft,
			supplierDomain.Supplier_Hotelbeds,
			// supplierDomain.Supplier_Giata,   // Uncomment when GIATA booking is ready
		},
		priceCacheSrv:  NewHotelPriceCacheService(r, supplierFactory),
		sessionService: commonService.NewSessionService(r),
		// 基于BIOrder的简化追踪，无需独立tracking service
	}
}
