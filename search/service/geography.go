package service

import (
	"context"

	pkgerr "github.com/pkg/errors"

	"hotel/content/domain"
	geoDomain "hotel/geography/domain"
	geoProto "hotel/geography/protocol"
	"hotel/search/protocol"
)

// Region
// @desc: Get region details by region id.
// @tags: openapi,Content,booking.hotelbyte.com/Content
func (s *SearchService) Region(ctx context.Context, req *protocol.RegionReq) (*protocol.RegionResp, error) {
	rg, err := s.geoSrv.GetRegion(ctx, req.Id)
	if err != nil {
		return nil, err
	}
	return &protocol.RegionResp{
		Region: rg,
	}, nil
}

// Regions
// @desc: Get region infos (standard or details) by region ancestor id
// @tags: openapi,Content,booking.hotelbyte.com/Content
func (s *SearchService) Regions(ctx context.Context, req *protocol.RegionsReq) (*protocol.RegionsResp, error) {
	if req.AncestorId > 0 {
		rgs, err := s.geoSrv.GetDescendants(ctx, req.AncestorId)
		if err != nil {
			return nil, err
		}
		return &protocol.RegionsResp{
			Regions: rgs,
		}, nil
	}
	return &protocol.RegionsResp{}, nil
}

// SearchCity
// @desc: search city by keyword,etc.
// @path: /city
// @tags: booking.hotelbyte.com/Content
func (s *SearchService) SearchCity(ctx context.Context, req *protocol.SearchCityReq) (*protocol.SearchCityResp, error) {
	geoResp, err := s.geoSrv.FuzzySearch(ctx, &geoProto.FuzzySearchReq{
		Keyword:      req.Keyword,
		ContentTypes: []geoProto.FuzzySearchContentType{geoProto.FuzzySearchContentType_City},
	})
	if err != nil {
		return nil, err
	}
	return convertGeoFuzzySearchResp2SearchCityResp(geoResp), nil
}
func convertGeoFuzzySearchResp2SearchCityResp(in *geoProto.FuzzySearchResp) *protocol.SearchCityResp {
	return &protocol.SearchCityResp{
		Regions: convertFuzzySearchItemList2SearchCityRegion(in.Candidates),
	}
}
func convertFuzzySearchItemList2SearchCityRegion(in []*geoProto.FuzzySearchItem) []*geoDomain.Region {
	out := make([]*geoDomain.Region, 0, len(in))

	// 优先展示地级市（RegionType_MultiCityVicinity）
	var multiCityRegions []*geoDomain.Region
	var otherRegions []*geoDomain.Region

	for _, v := range in {
		if v.Region == nil {
			continue
		}

		// NameFull优化已在索引构建时完成，这里只需要按类型分组
		if v.Region.Type == geoDomain.RegionType_MultiCityVicinity {
			multiCityRegions = append(multiCityRegions, v.Region)
		} else {
			otherRegions = append(otherRegions, v.Region)
		}
	}

	// 地级市优先，其他类型在后
	out = append(out, multiCityRegions...)
	out = append(out, otherRegions...)

	return out
}

// Anything
// @desc: search anything by keyword,etc. place, hotel are supported.
// @tags: booking.hotelbyte.com/Content
func (s *SearchService) Anything(ctx context.Context, req *protocol.SearchReq) (*protocol.SearchResp, error) {
	// try to search hotel first
	hs, err := s.contentSrv.FindSimilarHotel(ctx, (&domain.Hotel{}).WithNameEn(req.Keyword))
	if err != nil {
		return nil, pkgerr.Wrap(err, "find similar hotel")
	}
	resp := &protocol.SearchResp{}
	for _, h := range hs {
		resp.Candidates = append(resp.Candidates, &protocol.SearchItem{
			FuzzySearchItem: geoProto.FuzzySearchItem{
				Type: geoProto.FuzzySearchContentType_Hotel,
			},
			Hotel: h,
		})
	}

	geoResp, err := s.geoSrv.FuzzySearch(ctx, &geoProto.FuzzySearchReq{
		Keyword:     req.Keyword,
		RegionTypes: disabledRegionTypes,
	})
	if err != nil {
		return nil, pkgerr.Wrap(err, "geoSrv.FuzzySearch")
	}
	resp.Candidates = append(resp.Candidates, convertGeoFuzzySearchResp2SearchResp(geoResp)...)
	return resp, nil
}

var (
	disabledRegionTypes = []geoDomain.RegionType{
		geoDomain.RegionType_City,
		geoDomain.RegionType_MultiCityVicinity,
		geoDomain.RegionType_HighLevelRegion,
	}
)

func convertGeoFuzzySearchResp2SearchResp(in *geoProto.FuzzySearchResp) (out []*protocol.SearchItem) {
	for _, v := range in.Candidates {
		out = append(out, &protocol.SearchItem{
			FuzzySearchItem: *v,
		})
	}
	return out
}
