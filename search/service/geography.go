package service

import (
	"context"
	"strings"

	pkgerr "github.com/pkg/errors"

	"hotel/content/domain"
	geoDomain "hotel/geography/domain"
	geoProto "hotel/geography/protocol"
	"hotel/search/protocol"
)

// Region
// @desc: Get region details by region id.
// @tags: openapi,Content,booking.hotelbyte.com/Content
func (s *SearchService) Region(ctx context.Context, req *protocol.RegionReq) (*protocol.RegionResp, error) {
	rg, err := s.geoSrv.GetRegion(ctx, req.Id)
	if err != nil {
		return nil, err
	}
	return &protocol.RegionResp{
		Region: rg,
	}, nil
}

// Regions
// @desc: Get region infos (standard or details) by region ancestor id
// @tags: openapi,Content,booking.hotelbyte.com/Content
func (s *SearchService) Regions(ctx context.Context, req *protocol.RegionsReq) (*protocol.RegionsResp, error) {
	if req.AncestorId > 0 {
		rgs, err := s.geoSrv.GetDescendants(ctx, req.AncestorId)
		if err != nil {
			return nil, err
		}
		return &protocol.RegionsResp{
			Regions: rgs,
		}, nil
	}
	return &protocol.RegionsResp{}, nil
}

// SearchCity
// @desc: search city by keyword,etc.
// @path: /city
// @tags: booking.hotelbyte.com/Content
func (s *SearchService) SearchCity(ctx context.Context, req *protocol.SearchCityReq) (*protocol.SearchCityResp, error) {
	geoResp, err := s.geoSrv.FuzzySearch(ctx, &geoProto.FuzzySearchReq{
		Keyword:      req.Keyword,
		ContentTypes: []geoProto.FuzzySearchContentType{geoProto.FuzzySearchContentType_City},
	})
	if err != nil {
		return nil, err
	}
	return convertGeoFuzzySearchResp2SearchCityResp(geoResp), nil
}
func convertGeoFuzzySearchResp2SearchCityResp(in *geoProto.FuzzySearchResp) *protocol.SearchCityResp {
	return &protocol.SearchCityResp{
		Regions: convertFuzzySearchItemList2SearchCityRegion(in.Candidates),
	}
}
func convertFuzzySearchItemList2SearchCityRegion(in []*geoProto.FuzzySearchItem) []*geoDomain.Region {
	out := make([]*geoDomain.Region, 0, len(in))

	// 优先展示地级市（RegionType_MultiCityVicinity）
	var multiCityRegions []*geoDomain.Region
	var otherRegions []*geoDomain.Region

	for _, v := range in {
		if v.Region == nil {
			continue
		}

		// 优化NameFull，去掉冗余信息
		optimizedRegion := optimizeRegionNameFull(v.Region)

		if optimizedRegion.Type == geoDomain.RegionType_MultiCityVicinity {
			multiCityRegions = append(multiCityRegions, optimizedRegion)
		} else {
			otherRegions = append(otherRegions, optimizedRegion)
		}
	}

	// 地级市优先，其他类型在后
	out = append(out, multiCityRegions...)
	out = append(out, otherRegions...)

	return out
}

// optimizeRegionNameFull 优化region的NameFull字段，去掉冗余信息
// 例如："上海，上海，中国" -> "上海，中国"
func optimizeRegionNameFull(region *geoDomain.Region) *geoDomain.Region {
	if region == nil {
		return nil
	}

	// 创建region的副本，避免修改原始数据
	optimized := *region

	if region.NameFull == "" {
		return &optimized
	}

	// 按逗号分割NameFull
	parts := strings.Split(region.NameFull, ",")
	if len(parts) <= 1 {
		return &optimized
	}

	// 去掉空格并去重
	var cleanParts []string
	seen := make(map[string]bool)

	for _, part := range parts {
		trimmed := strings.TrimSpace(part)
		if trimmed != "" && !seen[trimmed] {
			cleanParts = append(cleanParts, trimmed)
			seen[trimmed] = true
		}
	}

	// 重新组合NameFull
	if len(cleanParts) > 0 {
		optimized.NameFull = strings.Join(cleanParts, ", ")
	}

	return &optimized
}

// Anything
// @desc: search anything by keyword,etc. place, hotel are supported.
// @tags: booking.hotelbyte.com/Content
func (s *SearchService) Anything(ctx context.Context, req *protocol.SearchReq) (*protocol.SearchResp, error) {
	// try to search hotel first
	hs, err := s.contentSrv.FindSimilarHotel(ctx, (&domain.Hotel{}).WithNameEn(req.Keyword))
	if err != nil {
		return nil, pkgerr.Wrap(err, "find similar hotel")
	}
	resp := &protocol.SearchResp{}
	for _, h := range hs {
		resp.Candidates = append(resp.Candidates, &protocol.SearchItem{
			FuzzySearchItem: geoProto.FuzzySearchItem{
				Type: geoProto.FuzzySearchContentType_Hotel,
			},
			Hotel: h,
		})
	}

	geoResp, err := s.geoSrv.FuzzySearch(ctx, &geoProto.FuzzySearchReq{
		Keyword:     req.Keyword,
		RegionTypes: disabledRegionTypes,
	})
	if err != nil {
		return nil, pkgerr.Wrap(err, "geoSrv.FuzzySearch")
	}
	resp.Candidates = append(resp.Candidates, convertGeoFuzzySearchResp2SearchResp(geoResp)...)
	return resp, nil
}

var (
	disabledRegionTypes = []geoDomain.RegionType{
		geoDomain.RegionType_City,
		geoDomain.RegionType_MultiCityVicinity,
		geoDomain.RegionType_HighLevelRegion,
	}
)

func convertGeoFuzzySearchResp2SearchResp(in *geoProto.FuzzySearchResp) (out []*protocol.SearchItem) {
	for _, v := range in.Candidates {
		out = append(out, &protocol.SearchItem{
			FuzzySearchItem: *v,
		})
	}
	return out
}
