import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { ElDialog, ElForm, ElSelect, ElButton, ElMessage, ElMessageBox } from 'element-plus'
import CancelOrderDialog from '@/components/booking/CancelOrderDialog.vue'
import { bookingApi } from '@/api/booking/bookingApi'

// Mock the booking API
vi.mock('@/api/booking/bookingApi', () => ({
  bookingApi: {
    cancelBooking: vi.fn()
  }
}))

// Mock Element Plus components
vi.mock('element-plus', async () => {
  const actual = await vi.importActual('element-plus')
  return {
    ...actual,
    ElMessage: {
      success: vi.fn(),
      error: vi.fn(),
      warning: vi.fn()
    },
    ElMessageBox: {
      confirm: vi.fn()
    }
  }
})

describe('CancelOrderDialog', () => {
  const mockOrderInfo = {
    orderId: '12345',
    hotelName: 'Test Hotel',
    checkIn: '2025-01-20',
    checkOut: '2025-01-22',
    amount: '¥500.00'
  }

  let wrapper: any

  beforeEach(() => {
    vi.clearAllMocks()
    wrapper = mount(CancelOrderDialog, {
      props: {
        visible: true,
        orderInfo: mockOrderInfo
      },
      global: {
        components: {
          ElDialog,
          ElForm,
          ElSelect,
          ElButton
        }
      }
    })
  })

  it('renders correctly with order information', () => {
    expect(wrapper.find('.order-info').exists()).toBe(true)
    expect(wrapper.text()).toContain('12345')
    expect(wrapper.text()).toContain('Test Hotel')
    expect(wrapper.text()).toContain('2025-01-20 - 2025-01-22')
    expect(wrapper.text()).toContain('¥500.00')
  })

  it('shows cancel reason options', () => {
    const select = wrapper.findComponent(ElSelect)
    expect(select.exists()).toBe(true)
  })

  it('shows custom reason input when "other" is selected', async () => {
    const vm = wrapper.vm
    vm.form.reason = 'other'
    await wrapper.vm.$nextTick()
    
    expect(wrapper.find('textarea').exists()).toBe(true)
  })

  it('validates required fields', async () => {
    const form = wrapper.findComponent(ElForm)
    const validateSpy = vi.spyOn(form.vm, 'validate')
    validateSpy.mockResolvedValue(false)

    const confirmButton = wrapper.find('button[type="danger"]')
    await confirmButton.trigger('click')

    expect(validateSpy).toHaveBeenCalled()
  })

  it('calls cancel API when form is valid and confirmed', async () => {
    const mockConfirm = vi.mocked(ElMessageBox.confirm)
    const mockCancelBooking = vi.mocked(bookingApi.cancelBooking)
    const mockSuccess = vi.mocked(ElMessage.success)

    mockConfirm.mockResolvedValue('confirm')
    mockCancelBooking.mockResolvedValue({})

    // Set form data
    const vm = wrapper.vm
    vm.form.reason = 'customer_request'
    vm.form.refundMethod = 'original'
    vm.form.notifyCustomer = true

    // Mock form validation
    const form = wrapper.findComponent(ElForm)
    const validateSpy = vi.spyOn(form.vm, 'validate')
    validateSpy.mockResolvedValue(true)

    const confirmButton = wrapper.find('button[type="danger"]')
    await confirmButton.trigger('click')

    await wrapper.vm.$nextTick()

    expect(mockConfirm).toHaveBeenCalledWith(
      '确定要取消这个订单吗？此操作不可撤销。',
      '确认取消',
      expect.objectContaining({
        confirmButtonText: '确定取消',
        cancelButtonText: '再想想',
        type: 'warning'
      })
    )

    expect(mockCancelBooking).toHaveBeenCalledWith({
      orderId: '12345',
      reason: '客户主动取消',
      refundMethod: 'original',
      notifyCustomer: true
    })

    expect(mockSuccess).toHaveBeenCalledWith('订单取消成功')
  })

  it('handles API errors gracefully', async () => {
    const mockConfirm = vi.mocked(ElMessageBox.confirm)
    const mockCancelBooking = vi.mocked(bookingApi.cancelBooking)
    const mockError = vi.mocked(ElMessage.error)

    mockConfirm.mockResolvedValue('confirm')
    mockCancelBooking.mockRejectedValue(new Error('API Error'))

    // Set form data
    const vm = wrapper.vm
    vm.form.reason = 'customer_request'

    // Mock form validation
    const form = wrapper.findComponent(ElForm)
    const validateSpy = vi.spyOn(form.vm, 'validate')
    validateSpy.mockResolvedValue(true)

    const confirmButton = wrapper.find('button[type="danger"]')
    await confirmButton.trigger('click')

    await wrapper.vm.$nextTick()

    expect(mockError).toHaveBeenCalledWith('取消订单失败，请重试')
  })

  it('emits success event when cancellation succeeds', async () => {
    const mockConfirm = vi.mocked(ElMessageBox.confirm)
    const mockCancelBooking = vi.mocked(bookingApi.cancelBooking)

    mockConfirm.mockResolvedValue('confirm')
    mockCancelBooking.mockResolvedValue({})

    // Set form data
    const vm = wrapper.vm
    vm.form.reason = 'customer_request'

    // Mock form validation
    const form = wrapper.findComponent(ElForm)
    const validateSpy = vi.spyOn(form.vm, 'validate')
    validateSpy.mockResolvedValue(true)

    const confirmButton = wrapper.find('button[type="danger"]')
    await confirmButton.trigger('click')

    await wrapper.vm.$nextTick()

    expect(wrapper.emitted('success')).toBeTruthy()
  })

  it('closes dialog when cancel button is clicked', async () => {
    const cancelButton = wrapper.find('button:not([type="danger"])')
    await cancelButton.trigger('click')

    expect(wrapper.emitted('update:visible')).toBeTruthy()
    expect(wrapper.emitted('update:visible')[0]).toEqual([false])
  })

  it('resets form when dialog opens', async () => {
    const vm = wrapper.vm
    
    // Set some form data
    vm.form.reason = 'customer_request'
    vm.form.customReason = 'test reason'
    vm.form.refundMethod = 'manual'
    vm.form.notifyCustomer = false

    // Simulate dialog opening
    await wrapper.setProps({ visible: false })
    await wrapper.setProps({ visible: true })

    expect(vm.form.reason).toBe('')
    expect(vm.form.customReason).toBe('')
    expect(vm.form.refundMethod).toBe('original')
    expect(vm.form.notifyCustomer).toBe(true)
  })

  it('shows loading state during submission', async () => {
    const mockConfirm = vi.mocked(ElMessageBox.confirm)
    const mockCancelBooking = vi.mocked(bookingApi.cancelBooking)

    mockConfirm.mockResolvedValue('confirm')
    // Make the API call hang
    mockCancelBooking.mockImplementation(() => new Promise(() => {}))

    // Set form data
    const vm = wrapper.vm
    vm.form.reason = 'customer_request'

    // Mock form validation
    const form = wrapper.findComponent(ElForm)
    const validateSpy = vi.spyOn(form.vm, 'validate')
    validateSpy.mockResolvedValue(true)

    const confirmButton = wrapper.find('button[type="danger"]')
    await confirmButton.trigger('click')

    await wrapper.vm.$nextTick()

    expect(vm.submitting).toBe(true)
  })

  it('validates custom reason when "other" is selected', async () => {
    const vm = wrapper.vm
    vm.form.reason = 'other'
    vm.form.customReason = ''

    const form = wrapper.findComponent(ElForm)
    const validateSpy = vi.spyOn(form.vm, 'validate')
    validateSpy.mockResolvedValue(false)

    const confirmButton = wrapper.find('button[type="danger"]')
    await confirmButton.trigger('click')

    expect(validateSpy).toHaveBeenCalled()
  })

  it('displays warning notice', () => {
    expect(wrapper.find('.warning-notice').exists()).toBe(true)
    expect(wrapper.text()).toContain('订单取消后无法恢复，请谨慎操作')
    expect(wrapper.text()).toContain('根据酒店政策，可能产生取消费用')
  })

  it('handles user cancellation of confirmation dialog', async () => {
    const mockConfirm = vi.mocked(ElMessageBox.confirm)
    mockConfirm.mockRejectedValue('cancel')

    // Set form data
    const vm = wrapper.vm
    vm.form.reason = 'customer_request'

    // Mock form validation
    const form = wrapper.findComponent(ElForm)
    const validateSpy = vi.spyOn(form.vm, 'validate')
    validateSpy.mockResolvedValue(true)

    const confirmButton = wrapper.find('button[type="danger"]')
    await confirmButton.trigger('click')

    await wrapper.vm.$nextTick()

    // Should not call the API
    expect(bookingApi.cancelBooking).not.toHaveBeenCalled()
    // Should not emit success
    expect(wrapper.emitted('success')).toBeFalsy()
  })
})
