import { test, expect } from '@playwright/test'

test.describe('用户管理模块 - 基础测试', () => {
  test('应该能够访问用户管理相关页面', async ({ page }) => {
    // 测试用户管理相关路由是否正常加载
    const userRoutes = [
      '/users/tenant-users',
      '/users/customer-users', 
      '/users/platform-users',
      '/system/user-center'
    ]
    
    for (const route of userRoutes) {
      console.log(`测试路由: ${route}`)
      
      try {
        await page.goto(`/#${route}`)
        
        // 等待页面加载
        await page.waitForLoadState('networkidle', { timeout: 10000 })
        
        // 验证页面正常加载，不显示404
        const bodyText = await page.textContent('body')
        expect(bodyText).not.toContain('404')
        expect(bodyText).not.toContain('页面不存在')
        
        console.log(`✅ 路由 ${route} 加载成功`)
      } catch (error) {
        console.error(`❌ 路由 ${route} 加载失败:`, error)
        // 继续测试其他路由
      }
    }
  })

  test('用户编辑页面应该正常加载', async ({ page }) => {
    // 测试用户编辑页面
    await page.goto('/#/users/user-edit/1')
    
    // 等待页面加载
    await page.waitForLoadState('networkidle', { timeout: 10000 })
    
    // 验证页面标题
    const title = await page.textContent('h2')
    expect(title).toContain('编辑用户')
    
    // 验证表单存在
    await expect(page.locator('.el-form')).toBeVisible()
  })

  test('个人中心页面应该正常加载', async ({ page }) => {
    // 测试个人中心页面
    await page.goto('/#/system/user-center')
    
    // 等待页面加载
    await page.waitForLoadState('networkidle', { timeout: 10000 })
    
    // 验证页面标题
    const title = await page.textContent('h2')
    expect(title).toContain('个人中心')
    
    // 验证个人信息卡片存在
    await expect(page.locator('.profile-card')).toBeVisible()
  })

  test('用户列表页面应该显示表格', async ({ page }) => {
    // 测试租户用户列表页面
    await page.goto('/#/users/tenant-users')
    
    // 等待页面加载
    await page.waitForLoadState('networkidle', { timeout: 10000 })
    
    // 验证表格存在
    await expect(page.locator('.el-table')).toBeVisible()
  })
}) 