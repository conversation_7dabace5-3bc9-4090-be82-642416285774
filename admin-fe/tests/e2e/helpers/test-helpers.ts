import { Page } from '@playwright/test'

export class TestHelpers {
  constructor(private page: Page) {}

  /**
   * 登录功能
   */
  async login(email: string, password: string) {
    // 导航到登录页面
    await this.page.goto('/#/auth/login')
    
    // 等待页面加载
    await this.page.waitForLoadState('networkidle')
    
    // 填写登录表单
    await this.page.fill('input[placeholder="请输入邮箱"]', email)
    await this.page.fill('input[placeholder="请输入密码"]', password)
    
    // 点击登录按钮
    await this.page.click('button:has-text("登录")')
    
    // 等待登录完成
    await this.page.waitForLoadState('networkidle')
    
    // 验证登录成功（检查是否跳转到首页或显示用户信息）
    await this.page.waitForURL(/\/#\/.*/, { timeout: 10000 })
  }

  /**
   * 等待元素出现
   */
  async waitForElement(selector: string, timeout = 5000) {
    await this.page.waitForSelector(selector, { timeout })
  }

  /**
   * 等待页面加载完成
   */
  async waitForPageLoad() {
    await this.page.waitForLoadState('networkidle')
  }

  /**
   * 模拟API响应
   */
  async mockApiResponse(url: string, response: any) {
    await this.page.route(url, async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify(response)
      })
    })
  }

  /**
   * 模拟API错误
   */
  async mockApiError(url: string, status: number, message: string) {
    await this.page.route(url, async route => {
      await route.fulfill({
        status,
        contentType: 'application/json',
        body: JSON.stringify({
          code: status,
          message
        })
      })
    })
  }

  /**
   * 获取元素文本
   */
  async getElementText(selector: string): Promise<string> {
    return await this.page.locator(selector).textContent() || ''
  }

  /**
   * 检查元素是否存在
   */
  async elementExists(selector: string): Promise<boolean> {
    return await this.page.locator(selector).count() > 0
  }

  /**
   * 点击元素
   */
  async clickElement(selector: string) {
    await this.page.click(selector)
  }

  /**
   * 填写表单
   */
  async fillForm(data: Record<string, string>) {
    for (const [selector, value] of Object.entries(data)) {
      await this.page.fill(selector, value)
    }
  }

  /**
   * 选择下拉选项
   */
  async selectOption(selector: string, value: string) {
    await this.page.selectOption(selector, value)
  }

  /**
   * 等待消息提示
   */
  async waitForMessage(type: 'success' | 'error' | 'warning' | 'info', timeout = 5000) {
    await this.page.waitForSelector(`.el-message--${type}`, { timeout })
  }

  /**
   * 截图
   */
  async takeScreenshot(name: string) {
    await this.page.screenshot({ path: `screenshots/${name}.png` })
  }
} 