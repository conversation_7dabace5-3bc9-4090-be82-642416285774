import { test, expect } from '@playwright/test'
import { TestHelpers } from './helpers/test-helpers'

test.describe('用户管理模块', () => {
  let page: any
  let testHelpers: TestHelpers

  test.beforeEach(async ({ page: testPage }) => {
    page = testPage
    testHelpers = new TestHelpers(page)
    
    // 登录
    await testHelpers.login('<EMAIL>', 'password123')
    
    // 等待页面加载
    await page.waitForLoadState('networkidle')
  })

  test('应该能够访问用户管理页面', async () => {
    // 导航到用户管理页面
    await page.goto('/#/users/tenant-users')
    
    // 验证页面标题
    await expect(page.locator('h1')).toContainText('租户用户')
    
    // 验证表格存在
    await expect(page.locator('.el-table')).toBeVisible()
  })

  test('应该能够编辑用户', async () => {
    // 导航到用户管理页面
    await page.goto('/#/users/tenant-users')
    
    // 等待表格加载
    await page.waitForSelector('.el-table')
    
    // 点击编辑按钮（假设第一行有编辑按钮）
    const editButton = page.locator('.el-table .el-button').first()
    await editButton.click()
    
    // 验证跳转到编辑页面
    await expect(page).toHaveURL(/\/users\/user-edit\/\d+/)
    
    // 验证编辑表单存在
    await expect(page.locator('h2')).toContainText('编辑用户')
  })

  test('应该能够访问个人中心', async () => {
    // 导航到个人中心
    await page.goto('/#/system/user-center')
    
    // 验证页面标题
    await expect(page.locator('h2')).toContainText('个人中心')
    
    // 验证个人信息卡片存在
    await expect(page.locator('.profile-card')).toBeVisible()
  })

  test('应该能够编辑个人资料', async () => {
    // 导航到个人中心
    await page.goto('/#/system/user-center')
    
    // 点击编辑资料按钮
    await page.locator('button:has-text("编辑资料")').click()
    
    // 验证切换到个人资料标签页
    await expect(page.locator('.el-tab-pane.is-active')).toContainText('个人资料')
    
    // 修改真实姓名
    const realNameInput = page.locator('input[placeholder="请输入真实姓名"]')
    await realNameInput.fill('测试用户')
    
    // 点击保存按钮
    await page.locator('button:has-text("保存修改")').click()
    
    // 验证成功消息
    await expect(page.locator('.el-message--success')).toBeVisible()
  })

  test('应该能够访问客户用户页面', async () => {
    // 导航到客户用户页面
    await page.goto('/#/users/customer-users')
    
    // 验证页面标题
    await expect(page.locator('h1')).toContainText('客户用户')
    
    // 验证表格存在
    await expect(page.locator('.el-table')).toBeVisible()
  })

  test('应该能够访问平台用户页面', async () => {
    // 导航到平台用户页面
    await page.goto('/#/users/platform-users')
    
    // 验证页面标题
    await expect(page.locator('h1')).toContainText('平台用户')
    
    // 验证表格存在
    await expect(page.locator('.el-table')).toBeVisible()
  })

  test('不应该出现404错误', async () => {
    // 测试用户管理相关路由
    const userRoutes = [
      '/users/tenant-users',
      '/users/customer-users', 
      '/users/platform-users',
      '/system/user-center'
    ]
    
    for (const route of userRoutes) {
      await page.goto(`/#${route}`)
      
      // 验证页面正常加载，不显示404
      await expect(page.locator('body')).not.toContainText('404')
      await expect(page.locator('body')).not.toContainText('页面不存在')
      
      // 等待页面加载完成
      await page.waitForLoadState('networkidle')
    }
  })
}) 