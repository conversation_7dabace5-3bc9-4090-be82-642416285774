import { test, expect } from '@playwright/test'

// Test data
const testOrder = {
  orderId: 'TEST_ORDER_12345',
  hotelName: 'Test Hotel Shanghai',
  checkIn: '2025-02-01',
  checkOut: '2025-02-03',
  amount: '¥1,200.00',
  status: 'confirmed'
}

test.describe('Cancel Order Flow', () => {
  test.beforeEach(async ({ page }) => {
    // Mock API responses
    await page.route('**/api/orders', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          data: {
            orders: [testOrder],
            total: 1,
            page: 1,
            pageSize: 10
          }
        })
      })
    })

    await page.route('**/api/orders/*/detail', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          data: {
            summary: {
              id: testOrder.orderId,
              status: 4 // OrderStateConfirmed
            },
            hotel: {
              name: testOrder.hotelName
            },
            booking: {
              checkIn: testOrder.checkIn,
              checkOut: testOrder.checkOut
            },
            payment: {
              totalAmount: 120000 // 1200.00 in cents
            }
          }
        })
      })
    })

    // Navigate to order management page
    await page.goto('/booking/order-management')
    await page.waitForLoadState('networkidle')
  })

  test('should cancel order from order list', async ({ page }) => {
    // Mock cancel API
    await page.route('**/api/trade/cancel', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ success: true })
      })
    })

    // Find and click cancel button for the test order
    const orderRow = page.locator(`tr:has-text("${testOrder.orderId}")`)
    await expect(orderRow).toBeVisible()

    const cancelButton = orderRow.locator('button:has-text("取消")')
    await expect(cancelButton).toBeVisible()
    await cancelButton.click()

    // Verify cancel dialog opens
    const dialog = page.locator('.el-dialog:has-text("取消订单")')
    await expect(dialog).toBeVisible()

    // Verify order information is displayed
    await expect(dialog).toContainText(testOrder.orderId)
    await expect(dialog).toContainText(testOrder.hotelName)
    await expect(dialog).toContainText(testOrder.checkIn)
    await expect(dialog).toContainText(testOrder.checkOut)

    // Select cancel reason
    const reasonSelect = dialog.locator('.el-select')
    await reasonSelect.click()
    await page.locator('.el-option:has-text("客户主动取消")').click()

    // Verify refund method is selected
    const originalRefund = dialog.locator('input[value="original"]')
    await expect(originalRefund).toBeChecked()

    // Verify notification checkbox is checked
    const notifyCheckbox = dialog.locator('input[type="checkbox"]')
    await expect(notifyCheckbox).toBeChecked()

    // Click confirm cancel button
    const confirmButton = dialog.locator('button:has-text("确认取消订单")')
    await confirmButton.click()

    // Handle confirmation dialog
    const confirmDialog = page.locator('.el-message-box')
    await expect(confirmDialog).toBeVisible()
    await expect(confirmDialog).toContainText('确定要取消这个订单吗？此操作不可撤销。')

    const confirmCancelButton = confirmDialog.locator('button:has-text("确定取消")')
    await confirmCancelButton.click()

    // Verify success message
    await expect(page.locator('.el-message--success')).toContainText('订单取消成功')

    // Verify dialog closes
    await expect(dialog).not.toBeVisible()
  })

  test('should cancel order from order detail page', async ({ page }) => {
    // Mock cancel API
    await page.route('**/api/trade/cancel', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ success: true })
      })
    })

    // Navigate to order detail page
    await page.goto(`/booking/orders/detail/${testOrder.orderId}`)
    await page.waitForLoadState('networkidle')

    // Find and click cancel button
    const cancelButton = page.locator('button:has-text("取消订单")')
    await expect(cancelButton).toBeVisible()
    await cancelButton.click()

    // Verify cancel dialog opens
    const dialog = page.locator('.el-dialog:has-text("取消订单")')
    await expect(dialog).toBeVisible()

    // Select cancel reason
    const reasonSelect = dialog.locator('.el-select')
    await reasonSelect.click()
    await page.locator('.el-option:has-text("行程变更")').click()

    // Click confirm cancel button
    const confirmButton = dialog.locator('button:has-text("确认取消订单")')
    await confirmButton.click()

    // Handle confirmation dialog
    const confirmDialog = page.locator('.el-message-box')
    await expect(confirmDialog).toBeVisible()
    const confirmCancelButton = confirmDialog.locator('button:has-text("确定取消")')
    await confirmCancelButton.click()

    // Verify success message
    await expect(page.locator('.el-message--success')).toContainText('订单取消成功')
  })

  test('should handle custom cancel reason', async ({ page }) => {
    // Mock cancel API
    await page.route('**/api/trade/cancel', async route => {
      const request = route.request()
      const postData = request.postDataJSON()
      
      // Verify custom reason is sent
      expect(postData.reason).toBe('Custom cancellation reason for testing')
      
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ success: true })
      })
    })

    // Click cancel button
    const orderRow = page.locator(`tr:has-text("${testOrder.orderId}")`)
    const cancelButton = orderRow.locator('button:has-text("取消")')
    await cancelButton.click()

    const dialog = page.locator('.el-dialog:has-text("取消订单")')
    await expect(dialog).toBeVisible()

    // Select "other" reason
    const reasonSelect = dialog.locator('.el-select')
    await reasonSelect.click()
    await page.locator('.el-option:has-text("其他原因")').click()

    // Verify custom reason textarea appears
    const customReasonTextarea = dialog.locator('textarea')
    await expect(customReasonTextarea).toBeVisible()

    // Fill custom reason
    await customReasonTextarea.fill('Custom cancellation reason for testing')

    // Click confirm cancel button
    const confirmButton = dialog.locator('button:has-text("确认取消订单")')
    await confirmButton.click()

    // Handle confirmation dialog
    const confirmDialog = page.locator('.el-message-box')
    const confirmCancelButton = confirmDialog.locator('button:has-text("确定取消")')
    await confirmCancelButton.click()

    // Verify success message
    await expect(page.locator('.el-message--success')).toContainText('订单取消成功')
  })

  test('should validate required fields', async ({ page }) => {
    // Click cancel button
    const orderRow = page.locator(`tr:has-text("${testOrder.orderId}")`)
    const cancelButton = orderRow.locator('button:has-text("取消")')
    await cancelButton.click()

    const dialog = page.locator('.el-dialog:has-text("取消订单")')
    await expect(dialog).toBeVisible()

    // Try to submit without selecting reason
    const confirmButton = dialog.locator('button:has-text("确认取消订单")')
    await confirmButton.click()

    // Verify validation error
    await expect(dialog).toContainText('请选择取消原因')
  })

  test('should handle API errors gracefully', async ({ page }) => {
    // Mock API error
    await page.route('**/api/trade/cancel', async route => {
      await route.fulfill({
        status: 500,
        contentType: 'application/json',
        body: JSON.stringify({ error: 'Internal server error' })
      })
    })

    // Click cancel button
    const orderRow = page.locator(`tr:has-text("${testOrder.orderId}")`)
    const cancelButton = orderRow.locator('button:has-text("取消")')
    await cancelButton.click()

    const dialog = page.locator('.el-dialog:has-text("取消订单")')
    
    // Select cancel reason
    const reasonSelect = dialog.locator('.el-select')
    await reasonSelect.click()
    await page.locator('.el-option:has-text("客户主动取消")').click()

    // Click confirm cancel button
    const confirmButton = dialog.locator('button:has-text("确认取消订单")')
    await confirmButton.click()

    // Handle confirmation dialog
    const confirmDialog = page.locator('.el-message-box')
    const confirmCancelButton = confirmDialog.locator('button:has-text("确定取消")')
    await confirmCancelButton.click()

    // Verify error message
    await expect(page.locator('.el-message--error')).toContainText('取消订单失败，请重试')
  })

  test('should support batch cancel', async ({ page }) => {
    // Mock multiple orders
    await page.route('**/api/orders', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          data: {
            orders: [
              { ...testOrder, orderId: 'ORDER_001' },
              { ...testOrder, orderId: 'ORDER_002' },
              { ...testOrder, orderId: 'ORDER_003' }
            ],
            total: 3,
            page: 1,
            pageSize: 10
          }
        })
      })
    })

    // Mock batch cancel API
    await page.route('**/api/trade/cancel', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ success: true })
      })
    })

    await page.reload()
    await page.waitForLoadState('networkidle')

    // Select multiple orders
    const selectAllCheckbox = page.locator('thead .el-checkbox')
    await selectAllCheckbox.click()

    // Verify batch actions appear
    const batchActions = page.locator('.batch-actions')
    await expect(batchActions).toBeVisible()

    // Click batch cancel button
    const batchCancelButton = batchActions.locator('button:has-text("批量取消")')
    await batchCancelButton.click()

    // Handle confirmation dialog
    const confirmDialog = page.locator('.el-message-box')
    await expect(confirmDialog).toContainText('确定要取消选中的 3 个订单吗？')
    
    const confirmButton = confirmDialog.locator('button:has-text("确定取消")')
    await confirmButton.click()

    // Verify success message
    await expect(page.locator('.el-message--success')).toContainText('成功取消 3 个订单')
  })

  test('should close dialog when clicking cancel', async ({ page }) => {
    // Click cancel button
    const orderRow = page.locator(`tr:has-text("${testOrder.orderId}")`)
    const cancelButton = orderRow.locator('button:has-text("取消")')
    await cancelButton.click()

    const dialog = page.locator('.el-dialog:has-text("取消订单")')
    await expect(dialog).toBeVisible()

    // Click cancel button in dialog
    const dialogCancelButton = dialog.locator('button:has-text("取消"):not(:has-text("确认取消订单"))')
    await dialogCancelButton.click()

    // Verify dialog closes
    await expect(dialog).not.toBeVisible()
  })
})
