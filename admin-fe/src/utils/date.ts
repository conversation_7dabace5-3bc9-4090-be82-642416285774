/**
 * 日期工具函数
 * 用于处理酒店搜索的日期选择，包括本地存储记忆和时区处理
 */

// 本地存储键名
const HOTEL_SEARCH_DATES_KEY = 'hotel_search_dates'

// 日期选择记录接口
export interface DateSelectionRecord {
  checkIn: string
  checkOut: string
  timestamp: number
}

/**
 * 获取用户本地时区的当前日期
 * @returns 当前日期字符串 (YYYY-MM-DD)
 */
export function getCurrentDate(): string {
  const now = new Date()
  const year = now.getFullYear()
  const month = String(now.getMonth() + 1).padStart(2, '0')
  const day = String(now.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
}

/**
 * 获取未来指定天数的日期
 * @param days 天数
 * @returns 日期字符串 (YYYY-MM-DD)
 */
export function getFutureDate(days: number): string {
  const future = new Date()
  future.setDate(future.getDate() + days)
  const year = future.getFullYear()
  const month = String(future.getMonth() + 1).padStart(2, '0')
  const day = String(future.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
}

/**
 * 从本地存储获取用户最近选择的日期
 * @returns 日期选择记录或null
 */
export function getStoredDates(): DateSelectionRecord | null {
  try {
    const stored = localStorage.getItem(HOTEL_SEARCH_DATES_KEY)
    if (!stored) return null
    
    const record: DateSelectionRecord = JSON.parse(stored)
    
    // 检查记录是否在7天内，过期则删除
    const now = Date.now()
    const sevenDaysInMs = 7 * 24 * 60 * 60 * 1000
    
    if (now - record.timestamp > sevenDaysInMs) {
      localStorage.removeItem(HOTEL_SEARCH_DATES_KEY)
      return null
    }
    
    return record
  } catch (error) {
    console.warn('Failed to parse stored dates:', error)
    localStorage.removeItem(HOTEL_SEARCH_DATES_KEY)
    return null
  }
}

/**
 * 保存用户选择的日期到本地存储
 * @param checkIn 入住日期
 * @param checkOut 退房日期
 */
export function storeDates(checkIn: string, checkOut: string): void {
  try {
    const record: DateSelectionRecord = {
      checkIn,
      checkOut,
      timestamp: Date.now()
    }
    localStorage.setItem(HOTEL_SEARCH_DATES_KEY, JSON.stringify(record))
  } catch (error) {
    console.warn('Failed to store dates:', error)
  }
}

/**
 * 获取默认的酒店搜索日期
 * 优先使用本地存储的记录，否则使用未来2天
 * @returns 包含checkIn和checkOut的对象
 */
export function getDefaultHotelDates(): { checkIn: string; checkOut: string } {
  // 尝试从本地存储获取用户最近的选择
  const stored = getStoredDates()
  
  if (stored) {
    // 验证存储的日期是否有效（不能是过去的日期）
    const today = getCurrentDate()
    if (stored.checkIn >= today) {
      return {
        checkIn: stored.checkIn,
        checkOut: stored.checkOut
      }
    }
  }
  
  // 如果没有存储记录或日期已过期，使用未来2天
  const checkIn = getFutureDate(2) // 未来2天作为入住日期
  const checkOut = getFutureDate(3) // 未来3天作为退房日期
  
  return { checkIn, checkOut }
}

/**
 * 验证日期是否有效
 * @param date 日期字符串
 * @returns 是否有效
 */
export function isValidDate(date: string): boolean {
  const dateObj = new Date(date)
  return !isNaN(dateObj.getTime())
}

/**
 * 验证入住和退房日期的逻辑
 * @param checkIn 入住日期
 * @param checkOut 退房日期
 * @returns 是否有效
 */
export function validateHotelDates(checkIn: string, checkOut: string): boolean {
  if (!isValidDate(checkIn) || !isValidDate(checkOut)) {
    return false
  }
  
  const checkInDate = new Date(checkIn)
  const checkOutDate = new Date(checkOut)
  const today = new Date()
  today.setHours(0, 0, 0, 0) // 重置时间为当天开始
  
  // 入住日期不能是过去的日期
  if (checkInDate < today) {
    return false
  }
  
  // 退房日期必须晚于入住日期
  if (checkOutDate <= checkInDate) {
    return false
  }
  
  return true
}

/**
 * 计算入住天数
 * @param checkIn 入住日期
 * @param checkOut 退房日期
 * @returns 入住天数
 */
export function calculateNights(checkIn: string, checkOut: string): number {
  if (!isValidDate(checkIn) || !isValidDate(checkOut)) {
    return 0
  }
  
  const checkInDate = new Date(checkIn)
  const checkOutDate = new Date(checkOut)
  const diffTime = checkOutDate.getTime() - checkInDate.getTime()
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  
  return diffDays
} 