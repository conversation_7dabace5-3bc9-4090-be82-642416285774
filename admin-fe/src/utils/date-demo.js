/**
 * 日期工具函数演示脚本
 * 在浏览器控制台中运行此脚本来测试日期功能
 */

// 模拟日期工具函数（简化版本）
function getCurrentDate() {
  const now = new Date()
  const year = now.getFullYear()
  const month = String(now.getMonth() + 1).padStart(2, '0')
  const day = String(now.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
}

function getFutureDate(days) {
  const future = new Date()
  future.setDate(future.getDate() + days)
  const year = future.getFullYear()
  const month = String(future.getMonth() + 1).padStart(2, '0')
  const day = String(future.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
}

function getStoredDates() {
  try {
    const stored = localStorage.getItem('hotel_search_dates')
    if (!stored) return null
    
    const record = JSON.parse(stored)
    const now = Date.now()
    const sevenDaysInMs = 7 * 24 * 60 * 60 * 1000
    
    if (now - record.timestamp > sevenDaysInMs) {
      localStorage.removeItem('hotel_search_dates')
      return null
    }
    
    return record
  } catch (error) {
    console.warn('Failed to parse stored dates:', error)
    localStorage.removeItem('hotel_search_dates')
    return null
  }
}

function storeDates(checkIn, checkOut) {
  try {
    const record = {
      checkIn,
      checkOut,
      timestamp: Date.now()
    }
    localStorage.setItem('hotel_search_dates', JSON.stringify(record))
    return true
  } catch (error) {
    console.warn('Failed to store dates:', error)
    return false
  }
}

function getDefaultHotelDates() {
  const stored = getStoredDates()
  
  if (stored) {
    const today = getCurrentDate()
    if (stored.checkIn >= today) {
      return {
        checkIn: stored.checkIn,
        checkOut: stored.checkOut
      }
    }
  }
  
  const checkIn = getFutureDate(2)
  const checkOut = getFutureDate(3)
  
  return { checkIn, checkOut }
}

// 演示函数
function demo() {
  console.log('=== 日期工具函数演示 ===')
  
  console.log('1. 当前日期:', getCurrentDate())
  console.log('2. 未来2天:', getFutureDate(2))
  console.log('3. 未来3天:', getFutureDate(3))
  
  console.log('4. 存储的日期:', getStoredDates())
  
  const defaultDates = getDefaultHotelDates()
  console.log('5. 默认酒店日期:', defaultDates)
  
  // 测试存储功能
  const testDates = {
    checkIn: getFutureDate(5),
    checkOut: getFutureDate(7)
  }
  
  console.log('6. 测试存储日期:', testDates)
  const stored = storeDates(testDates.checkIn, testDates.checkOut)
  console.log('7. 存储结果:', stored ? '成功' : '失败')
  
  console.log('8. 重新获取存储的日期:', getStoredDates())
  
  console.log('=== 演示完成 ===')
}

// 导出到全局作用域
window.dateDemo = {
  demo,
  getCurrentDate,
  getFutureDate,
  getStoredDates,
  storeDates,
  getDefaultHotelDates
}

console.log('日期演示脚本已加载，运行 window.dateDemo.demo() 开始演示') 