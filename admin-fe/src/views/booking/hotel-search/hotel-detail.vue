<template>
  <div class="hotel-detail" v-loading="loading">
    <template v-if="hotel">
      <!-- Hotel Header -->
      <div class="hotel-header">
        <div class="hotel-basic-info">
          <h1 class="hotel-name">{{ hotel.name }}</h1>
          <div class="hotel-rating">
            <el-rate v-model="hotel.rating" disabled show-score />
          </div>
          <p class="hotel-address">
            <el-icon><Location /></el-icon>
            {{ hotel.address }}, {{ hotel.city }}, {{ hotel.country }}
          </p>
        </div>
        <div class="hotel-actions">
          <el-button @click="goBack">
            <el-icon><ArrowLeft /></el-icon>
            {{ t('booking.hotel.backToSearch') }}
          </el-button>
        </div>
      </div>

      <!-- Hotel Images -->
      <div v-if="hotel.images && hotel.images.length > 0" class="hotel-images">
        <el-carousel height="400px" indicator-position="outside">
          <el-carousel-item v-for="(image, index) in hotel.images" :key="index">
            <img :src="image" :alt="`${hotel.name} - ${t('common.image')} ${index + 1}`" />
          </el-carousel-item>
        </el-carousel>
      </div>

      <!-- Hotel Info Tabs -->
      <div class="hotel-content">
        <el-row :gutter="24">
          <el-col :span="16">
            <el-tabs v-model="activeTab">
              <el-tab-pane :label="t('booking.hotel.hotelIntroduction')" name="description">
                <div class="hotel-description">
                  <h3>{{ t('booking.hotel.hotelDescription') }}</h3>
                  <p>{{ hotel.description }}</p>

                  <h3>{{ t('booking.hotel.hotelFacilities') }}</h3>
                  <div class="amenities-grid">
                    <div v-for="amenity in hotel.amenities" :key="amenity" class="amenity-item">
                      <el-icon><Check /></el-icon>
                      <span>{{ amenity }}</span>
                    </div>
                  </div>
                </div>
              </el-tab-pane>
              
              <el-tab-pane :label="t('booking.hotel.roomsAndPrices')" name="rooms">
                <div class="rooms-section" v-loading="roomsLoading">
                  <div v-if="rooms.length === 0" class="no-rooms">
                    <el-empty :description="t('booking.hotel.noRoomsAvailable')" />
                  </div>
                  <div v-else class="rooms-list">
                    <div v-for="(room, roomIndex) in rooms" :key="room.id" class="room-card">
                      <el-card shadow="hover">
                        <div class="room-content">
                          <div class="room-info">
                            <div class="room-number">{{ t('booking.hotel.roomType', '房型') }} {{ roomIndex + 1 }}</div>
                            <h4>{{ getI18nText(room.roomName) }}</h4>
                            <p>{{ getI18nText(room.roomDesc) }}</p>
                            <div class="room-details">
                              <span>{{ t('booking.hotel.maxOccupancy') }}: {{ room.occupancy }} {{ t('booking.hotel.people') }}</span>
                              <div class="room-amenities">
                                <el-tag v-for="amenity in (room.amenities || []).slice(0, 3)" :key="getI18nText(amenity)" size="small">
                                  {{ getI18nText(amenity) }}
                                </el-tag>
                              </div>
                            </div>
                          </div>
                          <div class="room-rates">
                            <div v-for="(rate, rateIndex) in room.rates" :key="rate.ratePkgId" class="rate-option">
                              <div class="rate-info">
                                <div class="rate-plan-name">
                                  <span class="plan-label">{{ t('booking.hotel.ratePlan', '价格方案') }}:</span>
                                  <span class="plan-name">{{ getMealTypeText(rate.meal) }}</span>
                                </div>
                                <p class="cancellation-policy">{{ rate.cancelPolicy ? getI18nText(rate.cancelPolicy) : t('booking.hotel.standardCancellation') }}</p>
                              </div>

                              <!-- 房间数量选择器 -->
                              <div class="room-quantity-selector">
                                <label class="quantity-label">{{ t('booking.hotel.roomQuantity', '房间数量') }}:</label>
                                <div class="quantity-controls">
                                  <el-button
                                    size="small"
                                    :disabled="searchParams.rooms <= 1"
                                    @click="updateRoomQuantity(searchParams.rooms - 1)"
                                    icon="Minus"
                                  />
                                  <span class="quantity-display">{{ searchParams.rooms }}</span>
                                  <el-button
                                    size="small"
                                    :disabled="searchParams.rooms >= 5"
                                    @click="updateRoomQuantity(searchParams.rooms + 1)"
                                    icon="Plus"
                                  />
                                </div>
                                <span class="quantity-hint">{{ t('booking.hotel.roomQuantityHint', '最多5间房') }}</span>
                              </div>

                              <div class="rate-pricing">
                                <div class="price-section">
                                  <div class="single-room-price">
                                    <span class="price-label">{{ t('booking.hotel.perRoom', '单间价格') }}:</span>
                                    <div class="price">
                                      <span class="currency">{{ formatCurrency(rate) }}</span>
                                      <span class="amount">{{ formatPrice(rate) }}</span>
                                      <span class="period">{{ t('booking.hotel.perNight') }}</span>
                                    </div>
                                  </div>

                                  <div class="total-price" v-if="searchParams.rooms > 1">
                                    <span class="price-label">{{ t('booking.hotel.totalPrice', '总价') }} ({{ searchParams.rooms }} {{ t('booking.hotel.rooms', '间房') }}):</span>
                                    <div class="price total">
                                      <span class="currency">{{ formatCurrency(rate) }}</span>
                                      <span class="amount">{{ (parseFloat(formatPrice(rate).replace(/,/g, '')) * searchParams.rooms).toLocaleString() }}</span>
                                      <span class="period">{{ t('booking.hotel.perNight') }}</span>
                                    </div>
                                  </div>
                                </div>

                                <el-button
                                  type="primary"
                                  size="large"
                                  :disabled="!rate.available"
                                  @click="selectRoom(room, rate)"
                                >
                                  {{ rate.available ? t('booking.hotel.selectRoom') : t('booking.hotel.soldOut') }}
                                </el-button>
                              </div>
                            </div>
                          </div>
                        </div>
                      </el-card>
                    </div>
                  </div>
                </div>
              </el-tab-pane>
              
              <el-tab-pane :label="t('booking.hotel.locationMap')" name="location">
                <div class="location-section">
                  <div class="map-container">
                    <div id="hotel-map" style="height: 400px; width: 100%;"></div>
                  </div>
                  <div class="location-info">
                    <h3>{{ t('booking.hotel.hotelLocation') }}</h3>
                    <p>{{ hotel.address }}, {{ hotel.city }}, {{ hotel.country }}</p>
                    <p>{{ t('booking.hotel.coordinates') }}: {{ hotel.location.latitude }}, {{ hotel.location.longitude }}</p>
                  </div>
                </div>
              </el-tab-pane>
            </el-tabs>
          </el-col>
          
          <!-- Booking Summary Sidebar -->
          <el-col :span="8">
            <el-card class="booking-summary" shadow="always">
              <template #header>
                <div class="booking-header">
                  <span>{{ t('booking.hotel.bookingInfo') }}</span>
                  <el-button
                    v-if="!isEditingDates"
                    type="text"
                    size="small"
                    @click="startEditingDates"
                    class="edit-dates-header-btn"
                  >
                    <el-icon><Edit /></el-icon>
                    {{ t('booking.hotel.editDates') }}
                  </el-button>
                </div>
              </template>

              <div class="search-params">
                <!-- Date editing section -->
                <div v-if="!isEditingDates" class="dates-display">
                  <div class="param-item">
                    <label>{{ t('booking.hotel.checkInDate') }}:</label>
                    <span>{{ searchParams.checkIn }}</span>
                  </div>
                  <div class="param-item">
                    <label>{{ t('booking.hotel.checkOutDate') }}:</label>
                    <span>{{ searchParams.checkOut }}</span>
                  </div>
                  <div class="param-item">
                    <label>{{ t('booking.hotel.stayDuration') }}:</label>
                    <span>{{ nightsCount }} {{ t('booking.hotel.nights') }}</span>
                  </div>
                </div>

                <div v-else class="dates-edit">
                  <div class="param-item">
                    <label>{{ t('booking.hotel.checkInDate') }}:</label>
                    <el-date-picker
                      v-model="tempSearchParams.checkIn"
                      type="date"
                      :placeholder="t('booking.search.selectDate')"
                      format="YYYY-MM-DD"
                      value-format="YYYY-MM-DD"
                      :disabled-date="disabledCheckInDate"
                      size="small"
                      style="width: 100%"
                    />
                  </div>
                  <div class="param-item">
                    <label>{{ t('booking.hotel.checkOutDate') }}:</label>
                    <el-date-picker
                      v-model="tempSearchParams.checkOut"
                      type="date"
                      :placeholder="t('booking.search.selectDate')"
                      format="YYYY-MM-DD"
                      value-format="YYYY-MM-DD"
                      :disabled-date="disabledCheckOutDate"
                      size="small"
                      style="width: 100%"
                    />
                  </div>
                  <div class="edit-dates-actions">
                    <el-button size="small" @click="cancelEditingDates">
                      {{ t('booking.hotel.cancelEdit') }}
                    </el-button>
                    <el-button type="primary" size="small" @click="confirmEditingDates" :loading="updatingRates">
                      {{ t('booking.hotel.confirmDates') }}
                    </el-button>
                  </div>
                </div>

                <div class="param-item">
                  <label>{{ t('booking.hotel.guestInfo') }}:</label>
                  <span>{{ searchParams.adults }} {{ t('booking.hotel.adults') }}, {{ searchParams.children }} {{ t('booking.hotel.children') }}</span>
                </div>
                <div class="param-item">
                  <label>{{ t('booking.hotel.roomCount') }}:</label>
                  <span>{{ searchParams.rooms }} {{ t('booking.hotel.rooms') }}</span>
                </div>
              </div>
              
              <el-divider />
              
              <div v-if="selectedRoom" class="selected-room">
                <h4>{{ t('booking.hotel.selectedRoom') }}</h4>
                <div class="room-summary">
                  <h5>{{ getI18nText(selectedRoom.room.roomName) }}</h5>
                  <p>{{ getMealTypeText(selectedRoom.rate.meal) }}</p>
                  <div class="price-summary">
                    <div class="price-item">
                      <span>{{ t('booking.hotel.roomFee') }} ({{ nightsCount }} {{ t('booking.hotel.nights') }}):</span>
                      <span>{{ formatCurrency(selectedRoom.rate) }} {{ (getRoomTotalPrice(selectedRoom.rate)).toLocaleString() }}</span>
                    </div>
                    <div class="price-item total">
                      <span>{{ t('booking.hotel.total') }}:</span>
                      <span>{{ formatCurrency(selectedRoom.rate) }} {{ (getRoomTotalPrice(selectedRoom.rate)).toLocaleString() }}</span>
                    </div>
                  </div>
                  <el-button type="primary" size="large" block @click="proceedToBooking">
                    {{ t('booking.hotel.bookNow') }}
                  </el-button>
                </div>
              </div>

              <div v-else class="no-selection">
                <p>{{ t('booking.hotel.pleaseSelectRoom') }}</p>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, nextTick, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { ElMessage } from 'element-plus'
import { Location, ArrowLeft, Check, Edit, Plus, Minus } from '@element-plus/icons-vue'
import { hotelSearchApi, type Hotel, type Room, type RoomRatePkg } from '@/api/booking/searchApi'
import L from 'leaflet'
import 'leaflet/dist/leaflet.css'

// Types
interface I18N {
  en?: string
  zh?: string
  ar?: string
}

// Frontend display hotel interface with processed i18n data
interface DisplayHotel {
  id: string
  name: string
  address: string
  city: string
  country: string
  rating: number
  images: string[]
  amenities: string[]
  description: string
  location: {
    latitude: number
    longitude: number
  }
  minPrice: number
  currency: string
}

const route = useRoute()
const router = useRouter()
const { locale, t } = useI18n()

/**
 * 根据当前语言设置获取多语言文本
 * @param i18nText 多语言文本对象 { zh: '', en: '', ar: '' }
 * @returns 当前语言对应的文本，带兜底逻辑
 */
function getI18nText(i18nText: any): string {
  if (!i18nText || typeof i18nText !== 'object') {
    return String(i18nText || '')
  }

  const currentLang = locale.value

  // 优先使用当前语言设置
  if (i18nText[currentLang]) {
    return i18nText[currentLang]
  }

  // 兜底逻辑：按优先级选择可用语言
  const fallbackOrder = ['zh', 'en', 'ar']

  for (const lang of fallbackOrder) {
    if (i18nText[lang]) {
      return i18nText[lang]
    }
  }

  // 最后兜底：返回第一个非空值或空字符串
  const firstValue = Object.values(i18nText).find(v => v && String(v).trim())
  return firstValue ? String(firstValue) : ''
}

/**
 * 获取餐食类型的显示文本
 * @param meal 餐食对象 { type: number, count: number, description?: string }
 * @returns 餐食类型的显示文本
 */
function getMealTypeText(meal: any): string {
  if (!meal || typeof meal !== 'object') {
    return t('booking.hotel.standardRate')
  }

  // 如果有description字段，优先使用description
  if (meal.description) {
    return getI18nText(meal.description)
  }

  // 根据type字段返回对应的餐食类型
  const mealTypeMap: Record<number, string> = {
    0: t('booking.hotel.mealType.none', '无餐'),
    1: t('booking.hotel.mealType.breakfast', '含早餐'),
    2: t('booking.hotel.mealType.lunch', '含午餐'),
    3: t('booking.hotel.mealType.dinner', '含晚餐'),
    4: t('booking.hotel.mealType.breakfastLunch', '含早午餐'),
    5: t('booking.hotel.mealType.breakfastDinner', '含早晚餐'),
    6: t('booking.hotel.mealType.lunchDinner', '含午晚餐'),
    7: t('booking.hotel.mealType.all', '含三餐')
  }

  return mealTypeMap[meal.type] || t('booking.hotel.standardRate')
}

/**
 * 获取房间总价（数字类型，用于计算）
 * @param rate 房型价格对象
 * @returns 房间总价数字
 */
function getRoomTotalPrice(rate: any): number {
  console.log('getRoomTotalPrice called with rate:', rate)

  if (!rate) {
    console.log('getRoomTotalPrice: rate is null/undefined')
    return 0
  }

  // 处理不同的价格数据结构
  let price = 0

  // 检查 rate.rate 字段（这是实际的价格对象）
  if (rate.rate) {
    console.log('getRoomTotalPrice: found rate.rate:', rate.rate)
    console.log('getRoomTotalPrice: rate.rate structure:', Object.keys(rate.rate))

    if (typeof rate.rate === 'number') {
      price = rate.rate
      console.log('getRoomTotalPrice: using rate.rate (number):', price)
    } else if (rate.rate.finalRate?.amount) {
      price = rate.rate.finalRate.amount
      console.log('getRoomTotalPrice: using rate.rate.finalRate.amount:', price)
    } else if (rate.rate.amount) {
      price = rate.rate.amount
      console.log('getRoomTotalPrice: using rate.rate.amount:', price)
    } else if (rate.rate.total) {
      price = rate.rate.total
      console.log('getRoomTotalPrice: using rate.rate.total:', price)
    } else if (rate.rate.net) {
      price = rate.rate.net
      console.log('getRoomTotalPrice: using rate.rate.net:', price)
    } else if (rate.rate.gross) {
      price = rate.rate.gross
      console.log('getRoomTotalPrice: using rate.rate.gross:', price)
    }
  }
  // 检查 dailyRates 字段（可能包含每日价格）
  else if (rate.dailyRates && Array.isArray(rate.dailyRates) && rate.dailyRates.length > 0) {
    console.log('getRoomTotalPrice: found dailyRates:', rate.dailyRates)
    const firstDayRate = rate.dailyRates[0]
    if (firstDayRate.amount) {
      price = firstDayRate.amount
      console.log('getRoomTotalPrice: using dailyRates[0].amount:', price)
    } else if (firstDayRate.rate) {
      price = firstDayRate.rate
      console.log('getRoomTotalPrice: using dailyRates[0].rate:', price)
    }
  }
  // 原有的兜底逻辑
  else if (typeof rate.price === 'number') {
    price = rate.price
    console.log('getRoomTotalPrice: using rate.price (number):', price)
  } else if (rate.price?.amount) {
    price = rate.price.amount
    console.log('getRoomTotalPrice: using rate.price.amount:', price)
  } else if (rate.amount) {
    price = rate.amount
    console.log('getRoomTotalPrice: using rate.amount:', price)
  } else {
    console.log('getRoomTotalPrice: no valid price field found, rate structure:', Object.keys(rate))
    console.log('getRoomTotalPrice: rate.rate details:', rate.rate)
    console.log('getRoomTotalPrice: rate.dailyRates details:', rate.dailyRates)
  }

  console.log('getRoomTotalPrice: final price per night:', price)
  const totalPrice = price * nightsCount.value
  console.log('getRoomTotalPrice: total price for', nightsCount.value, 'nights:', totalPrice)
  return totalPrice
}

/**
 * 格式化价格显示
 * @param rate 房型价格对象
 * @returns 格式化后的价格字符串
 */
function formatPrice(rate: any): string {
  console.log('formatPrice called with rate:', rate)

  if (!rate) {
    console.log('formatPrice: rate is null/undefined')
    return '0'
  }

  // 处理不同的价格数据结构
  let price = 0

  // 检查 rate.rate 字段（这是实际的价格对象）
  if (rate.rate) {
    console.log('formatPrice: found rate.rate:', rate.rate)
    console.log('formatPrice: rate.rate structure:', Object.keys(rate.rate))

    if (typeof rate.rate === 'number') {
      price = rate.rate
      console.log('formatPrice: using rate.rate (number):', price)
    } else if (rate.rate.finalRate?.amount) {
      price = rate.rate.finalRate.amount
      console.log('formatPrice: using rate.rate.finalRate.amount:', price)
    } else if (rate.rate.amount) {
      price = rate.rate.amount
      console.log('formatPrice: using rate.rate.amount:', price)
    } else if (rate.rate.total) {
      price = rate.rate.total
      console.log('formatPrice: using rate.rate.total:', price)
    } else if (rate.rate.net) {
      price = rate.rate.net
      console.log('formatPrice: using rate.rate.net:', price)
    } else if (rate.rate.gross) {
      price = rate.rate.gross
      console.log('formatPrice: using rate.rate.gross:', price)
    }
  }
  // 检查 dailyRates 字段（可能包含每日价格）
  else if (rate.dailyRates && Array.isArray(rate.dailyRates) && rate.dailyRates.length > 0) {
    console.log('formatPrice: found dailyRates:', rate.dailyRates)
    const firstDayRate = rate.dailyRates[0]
    if (firstDayRate.amount) {
      price = firstDayRate.amount
      console.log('formatPrice: using dailyRates[0].amount:', price)
    } else if (firstDayRate.rate) {
      price = firstDayRate.rate
      console.log('formatPrice: using dailyRates[0].rate:', price)
    }
  }
  // 原有的兜底逻辑
  else if (typeof rate.price === 'number') {
    price = rate.price
    console.log('formatPrice: using rate.price (number):', price)
  } else if (rate.price?.amount) {
    price = rate.price.amount
    console.log('formatPrice: using rate.price.amount:', price)
  } else if (rate.amount) {
    price = rate.amount
    console.log('formatPrice: using rate.amount:', price)
  } else {
    console.log('formatPrice: no valid price field found, rate structure:', Object.keys(rate))
    console.log('formatPrice: rate.rate details:', rate.rate)
    console.log('formatPrice: rate.dailyRates details:', rate.dailyRates)
  }

  console.log('formatPrice: final price:', price)
  return price.toLocaleString()
}

/**
 * 格式化货币符号
 * @param rate 房型价格对象
 * @returns 货币符号
 */
function formatCurrency(rate: any): string {
  if (!rate) return 'USD'

  // 处理不同的货币数据结构
  if (rate.rate?.finalRate?.currency && rate.rate.finalRate.currency.trim()) {
    return rate.rate.finalRate.currency
  } else if (rate.rate?.currency && rate.rate.currency.trim()) {
    return rate.rate.currency
  } else if (rate.dailyRates && Array.isArray(rate.dailyRates) && rate.dailyRates.length > 0) {
    const firstDayRate = rate.dailyRates[0]
    if (firstDayRate.currency && firstDayRate.currency.trim()) {
      return firstDayRate.currency
    }
  } else if (rate.price?.currency && rate.price.currency.trim()) {
    return rate.price.currency
  } else if (rate.currency && rate.currency.trim()) {
    return rate.currency
  }

  // 默认货币，可以根据实际需求调整
  return 'USD'
}

// Reactive data
const loading = ref(false)
const roomsLoading = ref(false)
const activeTab = ref('description')
const hotel = ref<DisplayHotel | null>(null)
const rooms = ref<Room[]>([])
const selectedRoom = ref<{ room: Room; rate: RoomRatePkg } | null>(null)
const map = ref<L.Map | null>(null)

// Date editing state
const isEditingDates = ref(false)
const updatingRates = ref(false)
const tempSearchParams = reactive({
  checkIn: '',
  checkOut: ''
})

// Search parameters from route query
const searchParams = reactive({
  hotelId: route.query.hotelId as string,
  checkIn: route.query.checkIn as string,
  checkOut: route.query.checkOut as string,
  adults: parseInt(route.query.adults as string) || 2,
  children: parseInt(route.query.children as string) || 0,
  rooms: parseInt(route.query.rooms as string) || 1
})

// Computed properties
const nightsCount = computed(() => {
  if (!searchParams.checkIn || !searchParams.checkOut) return 0
  const checkIn = new Date(searchParams.checkIn)
  const checkOut = new Date(searchParams.checkOut)
  return Math.ceil((checkOut.getTime() - checkIn.getTime()) / (1000 * 60 * 60 * 24))
})

// Methods
const loadHotelDetail = async () => {
  loading.value = true
  try {
    // Call real API to get hotel detail
    const hotelDetail = await hotelSearchApi.getHotelDetail(searchParams.hotelId)

    // Debug: Log the raw backend data
    console.log('Raw backend hotel detail:', hotelDetail)
    console.log('Hotel name field:', hotelDetail.name)
    console.log('Hotel address field:', hotelDetail.address)
    console.log('Hotel citySummary:', hotelDetail.citySummary)
    console.log('Hotel facilities:', hotelDetail.hotelFacilities)

    // Transform backend data to frontend format with proper i18n handling
    hotel.value = {
      id: hotelDetail.id?.toString() || searchParams.hotelId,
      name: getI18nText(hotelDetail.name) || t('booking.hotel.unknownHotel'),
      address: getI18nText(hotelDetail.address) || '',
      city: getI18nText(hotelDetail.citySummary?.city?.name) || '',
      country: getI18nText(hotelDetail.citySummary?.country?.name) || '',
      rating: hotelDetail.star || 0,
      images: (hotelDetail.hotelPictures || []).map(pic => pic.url).filter(Boolean) || [],
      amenities: (hotelDetail.hotelFacilities || []).map(facility => getI18nText(facility.name)).filter(Boolean) || [
        t('booking.hotel.defaultAmenities.wifi'),
        t('booking.hotel.defaultAmenities.pool'),
        t('booking.hotel.defaultAmenities.gym'),
        t('booking.hotel.defaultAmenities.restaurant'),
        t('booking.hotel.defaultAmenities.parking')
      ],
      description: getI18nText(hotelDetail.description) || t('booking.hotel.noDescription'),
      location: {
        latitude: hotelDetail.latlngCoordinator?.google?.lat || hotelDetail.latlngCoordinator?.gaode?.lat || 0,
        longitude: hotelDetail.latlngCoordinator?.google?.lng || hotelDetail.latlngCoordinator?.gaode?.lng || 0
      },
      minPrice: 0, // Will be updated from rates
      currency: 'USD'
    }

    console.log('Transformed hotel data:', hotel.value)
  } catch (error) {
    console.error('Failed to load hotel detail:', error)
    ElMessage.error(t('booking.hotel.loadHotelDetailFailed'))

    // Fallback to basic info if API fails
    hotel.value = {
      id: searchParams.hotelId,
      name: t('booking.hotel.hotelDetailLoadFailed'),
      address: '',
      city: '',
      country: '',
      rating: 0,
      images: [],
      amenities: [],
      description: t('booking.hotel.retryLater'),
      location: { latitude: 0, longitude: 0 },
      minPrice: 0,
      currency: 'USD'
    }
  } finally {
    loading.value = false
  }
}

const loadHotelRates = async () => {
  if (!hotel.value) return
  
  roomsLoading.value = true
  try {
    const response = await hotelSearchApi.getHotelRates(hotel.value.id, {
      checkIn: searchParams.checkIn,
      checkOut: searchParams.checkOut,
      adults: searchParams.adults,
      children: searchParams.children,
      rooms: searchParams.rooms
    })

    console.log('Raw hotel rates response:', response)
    console.log('Response.rooms:', response.rooms)
    console.log('First room data:', response.rooms?.[0])
    console.log('First rate data:', response.rooms?.[0]?.rates?.[0])

    rooms.value = response.rooms
  } catch (error) {
    console.error('Failed to load hotel rates:', error)
    ElMessage.error(t('booking.hotel.loadRatesFailed'))
  } finally {
    roomsLoading.value = false
  }
}

const selectRoom = (room: Room, rate: RoomRatePkg) => {
  selectedRoom.value = { room, rate }
  ElMessage.success(`${t('booking.hotel.selectedMessage')} ${getI18nText(room.roomName)} - ${rate.ratePkgId}`)
}

// 更新房间数量
const updateRoomQuantity = async (newQuantity: number) => {
  if (newQuantity < 1 || newQuantity > 5) return

  searchParams.rooms = newQuantity

  // 更新URL参数
  await router.replace({
    query: {
      ...route.query,
      rooms: newQuantity.toString()
    }
  })

  // 重新加载房间价格
  await loadHotelRates()

  ElMessage.success(t('booking.hotel.roomQuantityUpdated', `已更新为${newQuantity}间房`))
}

const proceedToBooking = () => {
  if (!selectedRoom.value) return

  router.push({
    path: '/booking/hotel-search/book',
    query: {
      hotelId: searchParams.hotelId,
      roomId: selectedRoom.value.room.id,
      ratePkgId: selectedRoom.value.rate.ratePkgId,
      checkIn: searchParams.checkIn,
      checkOut: searchParams.checkOut,
      adults: searchParams.adults.toString(),
      children: searchParams.children.toString(),
      rooms: searchParams.rooms.toString()
    }
  })
}

const goBack = () => {
  router.back()
}

// Date editing methods
const startEditingDates = () => {
  tempSearchParams.checkIn = searchParams.checkIn
  tempSearchParams.checkOut = searchParams.checkOut
  isEditingDates.value = true
}

const cancelEditingDates = () => {
  isEditingDates.value = false
  tempSearchParams.checkIn = ''
  tempSearchParams.checkOut = ''
}

const confirmEditingDates = async () => {
  if (!tempSearchParams.checkIn || !tempSearchParams.checkOut) {
    ElMessage.error('请选择有效的入住和退房日期')
    return
  }

  if (new Date(tempSearchParams.checkOut) <= new Date(tempSearchParams.checkIn)) {
    ElMessage.error('退房日期必须晚于入住日期')
    return
  }

  updatingRates.value = true
  try {
    // Update search parameters
    searchParams.checkIn = tempSearchParams.checkIn
    searchParams.checkOut = tempSearchParams.checkOut

    // Update URL parameters
    await router.replace({
      query: {
        ...route.query,
        checkIn: searchParams.checkIn,
        checkOut: searchParams.checkOut
      }
    })

    // Clear selected room since prices may have changed
    selectedRoom.value = null

    // Reload hotel rates with new dates
    await loadHotelRates()

    isEditingDates.value = false
    ElMessage.success(t('booking.hotel.datesUpdated'))
  } catch (error) {
    console.error('Failed to update dates:', error)
    ElMessage.error('更新日期失败，请重试')
  } finally {
    updatingRates.value = false
  }
}

// Date validation functions (copied from hotel search page)
const disabledCheckInDate = (time: Date) => {
  const today = new Date()
  today.setHours(0, 0, 0, 0) // 重置时间为当天开始
  return time.getTime() < today.getTime() // 禁用过去的日期
}

const disabledCheckOutDate = (time: Date) => {
  if (!tempSearchParams.checkIn) return false
  const checkInDate = new Date(tempSearchParams.checkIn)
  return time.getTime() <= checkInDate.getTime()
}

// Initialize map
const initMap = async () => {
  if (!hotel.value || !hotel.value.location.latitude || !hotel.value.location.longitude) {
    console.log('Map initialization skipped: missing hotel location data', {
      hotel: !!hotel.value,
      latitude: hotel.value?.location.latitude,
      longitude: hotel.value?.location.longitude
    })
    return
  }

  await nextTick()

  try {
    // Destroy existing map if it exists
    if (map.value) {
      map.value.remove()
      map.value = null
    }

    // Initialize map
    map.value = L.map('hotel-map').setView(
      [hotel.value.location.latitude, hotel.value.location.longitude],
      15
    )

    // Add tile layer
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
      attribution: '© OpenStreetMap contributors'
    }).addTo(map.value)

    // Add marker for hotel
    L.marker([hotel.value.location.latitude, hotel.value.location.longitude])
      .addTo(map.value)
      .bindPopup(`<b>${hotel.value.name}</b><br>${hotel.value.address}`)
      .openPopup()

    console.log('Map initialized successfully', {
      latitude: hotel.value.location.latitude,
      longitude: hotel.value.location.longitude
    })
  } catch (error) {
    console.error('Failed to initialize map:', error)
  }
}

// Initialize
onMounted(async () => {
  if (!searchParams.hotelId) {
    ElMessage.error(t('booking.hotel.missingHotelId'))
    router.push('/booking/hotel-search')
    return
  }

  await loadHotelDetail()
  loadHotelRates()

  // Initialize map after hotel data is loaded
  setTimeout(() => {
    initMap()
  }, 100)
})

// 监听语言变化，强制重新渲染组件
watch(locale, () => {
  // 语言变化时，可以重新加载数据或强制更新
  nextTick(() => {
    // 强制更新组件
  })
})

// 监听标签页切换，当切换到地图标签时初始化地图
watch(activeTab, (newTab) => {
  if (newTab === 'location') {
    setTimeout(() => {
      initMap()
    }, 100)
  }
})
</script>

<style scoped lang="scss">
.hotel-detail {
  padding: 24px;
  
  .hotel-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 24px;
    
    .hotel-basic-info {
      .hotel-name {
        font-size: 32px;
        font-weight: 600;
        color: #303133;
        margin: 0 0 12px 0;
      }
      
      .hotel-address {
        color: #606266;
        font-size: 16px;
        margin: 8px 0 0 0;
        display: flex;
        align-items: center;
        gap: 6px;
      }
    }
  }
  
  .hotel-images {
    margin-bottom: 32px;
    
    .el-carousel__item img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
  
  .hotel-content {
    .hotel-description {
      h3 {
        font-size: 18px;
        font-weight: 600;
        color: #303133;
        margin: 0 0 16px 0;
      }
      
      p {
        color: #606266;
        line-height: 1.6;
        margin-bottom: 24px;
      }
      
      .amenities-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 12px;
        
        .amenity-item {
          display: flex;
          align-items: center;
          gap: 8px;
          color: #67C23A;
        }
      }
    }
    
    .rooms-section {
      .rooms-list {
        .room-card {
          margin-bottom: 16px;
          
          .room-content {
            display: flex;
            gap: 24px;
            
            .room-info {
              flex: 1;

              .room-number {
                display: inline-block;
                background-color: #409eff;
                color: white;
                padding: 4px 12px;
                border-radius: 16px;
                font-size: 12px;
                font-weight: 500;
                margin-bottom: 12px;
              }

              h4 {
                font-size: 18px;
                font-weight: 600;
                color: #303133;
                margin: 0 0 8px 0;
              }

              p {
                color: #606266;
                margin-bottom: 12px;
              }
              
              .room-details {
                display: flex;
                flex-direction: column;
                gap: 8px;
                
                .room-amenities {
                  display: flex;
                  gap: 6px;
                  flex-wrap: wrap;
                }
              }
            }
            
            .room-rates {
              width: 300px;
              
              .rate-option {
                border: 1px solid #ebeef5;
                border-radius: 8px;
                padding: 16px;
                margin-bottom: 12px;
                
                &:last-child {
                  margin-bottom: 0;
                }
                
                .rate-info {
                  margin-bottom: 12px;

                  .rate-plan-name {
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    margin-bottom: 8px;

                    .plan-label {
                      font-size: 13px;
                      color: #606266;
                      font-weight: 500;
                    }

                    .plan-name {
                      font-size: 14px;
                      font-weight: 600;
                      color: #303133;
                      background-color: #f0f9ff;
                      padding: 2px 8px;
                      border-radius: 4px;
                      border: 1px solid #e1f5fe;
                    }
                  }

                  .rate-features {
                    display: flex;
                    gap: 6px;
                    margin-bottom: 8px;
                  }

                  .cancellation-policy {
                    font-size: 12px;
                    color: #909399;
                    margin: 0;
                  }
                }

                .room-quantity-selector {
                  margin-bottom: 16px;
                  padding: 12px;
                  background-color: #f8f9fa;
                  border-radius: 6px;
                  border: 1px solid #e9ecef;

                  .quantity-label {
                    display: block;
                    font-size: 14px;
                    font-weight: 500;
                    color: #495057;
                    margin-bottom: 8px;
                  }

                  .quantity-controls {
                    display: flex;
                    align-items: center;
                    gap: 12px;
                    margin-bottom: 6px;

                    .quantity-display {
                      display: inline-flex;
                      align-items: center;
                      justify-content: center;
                      min-width: 40px;
                      height: 32px;
                      background-color: #fff;
                      border: 2px solid #409eff;
                      border-radius: 4px;
                      font-size: 16px;
                      font-weight: 600;
                      color: #409eff;
                    }
                  }

                  .quantity-hint {
                    font-size: 12px;
                    color: #6c757d;
                  }
                }
                
                .rate-pricing {
                  display: flex;
                  flex-direction: column;
                  gap: 12px;

                  .price-section {
                    .single-room-price, .total-price {
                      display: flex;
                      justify-content: space-between;
                      align-items: center;
                      margin-bottom: 8px;

                      .price-label {
                        font-size: 13px;
                        color: #606266;
                        font-weight: 500;
                      }

                      .price {
                        .currency {
                          font-size: 14px;
                          color: #606266;
                        }

                        .amount {
                          font-size: 18px;
                          font-weight: 600;
                          color: #E6A23C;
                          margin: 0 4px;
                        }

                        .period {
                          font-size: 12px;
                          color: #909399;
                        }

                        &.total {
                          .amount {
                            font-size: 20px;
                            color: #67C23A;
                          }
                        }
                      }
                    }

                    .total-price {
                      padding-top: 8px;
                      border-top: 1px solid #ebeef5;
                      margin-bottom: 0;
                    }
                  }

                  .el-button {
                    margin-top: 8px;
                  }
                }
              }
            }
          }
        }
      }
    }
    
    .location-section {
      .map-container {
        margin-bottom: 16px;
        border-radius: 8px;
        overflow: hidden;
      }
      
      .location-info {
        h3 {
          font-size: 18px;
          font-weight: 600;
          color: #303133;
          margin: 0 0 12px 0;
        }
        
        p {
          color: #606266;
          margin: 0 0 8px 0;
        }
      }
    }
    
    .booking-summary {
      position: sticky;
      top: 24px;

      .booking-header {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .edit-dates-header-btn {
          color: #409eff;
          font-size: 12px;
          padding: 4px 8px;

          .el-icon {
            margin-right: 4px;
          }

          &:hover {
            background-color: #ecf5ff;
          }
        }
      }
      
      .search-params {
        .param-item {
          display: flex;
          justify-content: space-between;
          margin-bottom: 8px;

          label {
            color: #606266;
            font-weight: 500;
          }

          span {
            color: #303133;
          }
        }



        .dates-edit {
          .param-item {
            flex-direction: column;
            align-items: stretch;
            margin-bottom: 12px;

            label {
              margin-bottom: 4px;
            }
          }

          .edit-dates-actions {
            display: flex;
            gap: 8px;
            justify-content: flex-end;
            margin-top: 12px;
          }
        }
      }
      
      .selected-room {
        h4 {
          font-size: 16px;
          font-weight: 600;
          color: #303133;
          margin: 0 0 12px 0;
        }
        
        .room-summary {
          h5 {
            font-size: 14px;
            font-weight: 600;
            color: #303133;
            margin: 0 0 4px 0;
          }
          
          p {
            color: #606266;
            font-size: 14px;
            margin: 0 0 12px 0;
          }
          
          .price-summary {
            margin-bottom: 16px;
            
            .price-item {
              display: flex;
              justify-content: space-between;
              margin-bottom: 8px;
              
              &.total {
                font-weight: 600;
                font-size: 16px;
                color: #E6A23C;
                border-top: 1px solid #ebeef5;
                padding-top: 8px;
              }
            }
          }
        }
      }
      
      .no-selection {
        text-align: center;
        color: #909399;
        padding: 24px 0;
      }
    }
  }
}
</style>
