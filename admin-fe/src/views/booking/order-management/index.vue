<template>
  <div class="order-management">
    <!-- Header -->
    <div class="page-header">
      <h1 class="page-title">{{ $t('booking.orders.title', '订单管理') }}</h1>
      <div class="header-actions">
        <el-button @click="exportOrders" :loading="exporting">
          <el-icon><Download /></el-icon>
          {{ $t('booking.orders.export', '导出订单') }}
        </el-button>
        <el-button type="primary" @click="refreshOrders">
          <el-icon><Refresh /></el-icon>
          {{ $t('booking.orders.refresh', '刷新') }}
        </el-button>
      </div>
    </div>

    <!-- Search and Filters -->
    <el-card class="filter-card" shadow="never">
      <el-form :model="searchForm" inline>
        <el-form-item :label="$t('booking.orders.orderId', '订单号')">
          <el-input
            v-model="searchForm.orderId"
            :placeholder="$t('booking.orders.enterOrderId', '输入订单号')"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        
        <el-form-item :label="$t('booking.orders.hotelName', '酒店名称')">
          <el-input
            v-model="searchForm.hotelName"
            :placeholder="$t('booking.orders.enterHotelName', '输入酒店名称')"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        
        <el-form-item :label="$t('booking.orders.guestName', '客人姓名')">
          <el-input
            v-model="searchForm.guestName"
            :placeholder="$t('booking.orders.enterGuestName', '输入客人姓名')"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        
        <el-form-item :label="$t('booking.orders.status', '订单状态')">
          <el-select v-model="searchForm.status" :placeholder="$t('booking.orders.selectStatus', '选择状态')" clearable style="width: 150px">
            <el-option label="全部" value="" />
            <el-option label="已创建" value="created" />
            <el-option label="已支付" value="paid" />
            <el-option label="待确认" value="pending" />
            <el-option label="已确认" value="confirmed" />
            <el-option label="已完成" value="completed" />
            <el-option label="已取消" value="cancelled" />
            <el-option label="取消中" value="cancelling" />
            <el-option label="退款中" value="refunding" />
          </el-select>
        </el-form-item>
        
        <el-form-item :label="$t('booking.orders.dateRange', '日期范围')">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            style="width: 240px"
          />
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            {{ $t('booking.orders.search', '搜索') }}
          </el-button>
          <el-button @click="resetSearch">
            {{ $t('booking.orders.reset', '重置') }}
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- Orders Table -->
    <el-card class="table-card" shadow="never">
      <el-table
        :data="orders"
        v-loading="loading"
        stripe
        @selection-change="handleSelectionChange"
        data-cy="order-list"
      >
        <el-table-column type="selection" width="55" />
        
        <el-table-column prop="orderId" :label="$t('booking.orders.orderId', '订单号')" width="150" fixed="left">
          <template #default="{ row }">
            <el-link type="primary" @click="viewOrderDetail(row.orderId)" data-cy="order-id">
              {{ row.orderId }}
            </el-link>
          </template>
        </el-table-column>
        
        <el-table-column prop="hotel.name" :label="$t('booking.orders.hotelName', '酒店名称')" width="200" />
        
        <el-table-column :label="$t('booking.orders.guestInfo', '客人信息')" width="180">
          <template #default="{ row }">
            <div>
              <div>{{ row.guests[0]?.firstName }} {{ row.guests[0]?.lastName }}</div>
              <div class="text-secondary">{{ row.guests[0]?.email }}</div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column :label="$t('booking.orders.stayDates', '住宿日期')" width="200">
          <template #default="{ row }">
            <div>
              <div>{{ $t('booking.orders.checkIn', '入住') }}: {{ row.checkIn }}</div>
              <div>{{ $t('booking.orders.checkOut', '退房') }}: {{ row.checkOut }}</div>
              <div class="text-secondary">{{ row.nights }} {{ $t('booking.orders.nights', '晚') }}</div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column :label="$t('booking.orders.roomInfo', '房间信息')" width="150">
          <template #default="{ row }">
            <div>
              <div>{{ getI18nText(row.room.roomName) }}</div>
              <div class="text-secondary">{{ row.adults }}成人 {{ row.children }}儿童</div>
              <div class="text-secondary">{{ row.rooms }}间房</div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="totalPrice" :label="$t('booking.orders.totalPrice', '总价')" width="120">
          <template #default="{ row }">
            <div class="price-cell">
              <span class="currency">{{ row.currency }}</span>
              <span class="amount">{{ row.totalPrice.toLocaleString() }}</span>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="status" :label="$t('booking.orders.status', '状态')" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="createdAt" :label="$t('booking.orders.createdAt', '创建时间')" width="160">
          <template #default="{ row }">
            {{ formatDateTime(row.createdAt) }}
          </template>
        </el-table-column>
        
        <el-table-column :label="$t('booking.orders.actions', '操作')" width="200" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click="viewOrderDetail(row.orderId)">
              {{ $t('booking.orders.view', '查看') }}
            </el-button>
            <el-button
              v-if="canCancelOrder(row.status)"
              size="small"
              type="danger"
              @click="cancelOrder(row.orderId)"
            >
              {{ $t('booking.orders.cancel', '取消') }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- Pagination -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- Batch Actions -->
    <div v-if="selectedOrders.length > 0" class="batch-actions">
      <el-card shadow="never">
        <div class="batch-content">
          <span>{{ $t('booking.orders.selected', '已选择') }} {{ selectedOrders.length }} {{ $t('booking.orders.items', '项') }}</span>
          <div class="batch-buttons">
            <el-button @click="batchExport">
              {{ $t('booking.orders.batchExport', '批量导出') }}
            </el-button>
            <el-button type="danger" @click="batchCancel">
              {{ $t('booking.orders.batchCancel', '批量取消') }}
            </el-button>
          </div>
        </div>
      </el-card>
    </div>

    <!-- Order Detail Dialog -->
    <OrderDetailDialog
      v-model:visible="detailDialogVisible"
      :order-id="selectedOrderId"
      @refresh="refreshOrders"
    />

    <!-- Cancel Order Dialog -->
    <CancelOrderDialog
      v-model:visible="cancelDialogVisible"
      :order-info="cancelOrderInfo"
      @success="handleCancelSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox, ElLoading } from 'element-plus'
import { Download, Refresh, Search } from '@element-plus/icons-vue'
import { orderApi, type Order, type OrderListRequest } from '@/api/booking/orderApi'
import { bookingApi } from '@/api/booking/bookingApi'
import { useI18nText, useStarlingText } from '@/utils/i18n'
import { useI18n } from 'vue-i18n'
import OrderDetailDialog from './order-detail-dialog.vue'
import CancelOrderDialog from '@/components/booking/CancelOrderDialog.vue'

const route = useRoute()
const router = useRouter()
const { t } = useI18n()
const getI18nText = useI18nText()
const getStarlingText = useStarlingText()

// Reactive data
const loading = ref(false)
const exporting = ref(false)
const orders = ref<Order[]>([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(20)
const selectedOrders = ref<Order[]>([])
const detailDialogVisible = ref(false)
const selectedOrderId = ref('')
const cancelDialogVisible = ref(false)
const cancelOrderInfo = ref({
  orderId: '',
  hotelName: '',
  checkIn: '',
  checkOut: '',
  amount: ''
})

// Search form
const searchForm = reactive<OrderListRequest>({
  orderId: '',
  hotelName: '',
  guestName: '',
  status: undefined
})

const dateRange = ref<[string, string] | null>(null)

// Computed properties
const searchParams = computed(() => ({
  ...searchForm,
  startDate: dateRange.value?.[0],
  endDate: dateRange.value?.[1],
  page: currentPage.value,
  pageSize: pageSize.value
}))

// Methods
const mapOrderStatus = (status: number) => {
  switch (status) {
    case 1: return 'created'      // OrderStateCreated
    case 2: return 'paid'         // OrderStatePaid
    case 3: return 'pending'      // OrderStateNeedSupplierConfirmed
    case 4: return 'confirmed'    // OrderStateConfirmed
    case 5: return 'completed'    // OrderStateCompleted
    case 6: return 'cancelled'    // OrderStateCancelled
    case 7: return 'cancelling'   // OrderStateNeedCancel
    case 8: return 'refunding'    // OrderStateNeedRefund
    default: return 'pending'
  }
}

const getStatusType = (status: string) => {
  switch (status) {
    case 'created': return 'info'
    case 'paid': return 'primary'
    case 'pending': return 'warning'
    case 'confirmed': return 'success'
    case 'completed': return 'success'
    case 'cancelled': return 'danger'
    case 'cancelling': return 'warning'
    case 'refunding': return 'warning'
    default: return 'info'
  }
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'created': return '已创建'
    case 'paid': return '已支付'
    case 'pending': return '待确认'
    case 'confirmed': return '已确认'
    case 'completed': return '已完成'
    case 'cancelled': return '已取消'
    case 'cancelling': return '取消中'
    case 'refunding': return '退款中'
    default: return '未知状态'
  }
}

const formatDateTime = (dateTime: string | Date) => {
  if (!dateTime) return '-'
  const date = new Date(dateTime)
  return date.toLocaleString('zh-CN')
}

// Helper functions for data extraction
const calculateNights = (checkIn: number | string, checkOut: number | string): number => {
  if (!checkIn || !checkOut) return 1

  try {
    const checkInStr = String(checkIn)
    const checkOutStr = String(checkOut)

    // Parse YYYYMMDD format
    const checkInDate = new Date(
      parseInt(checkInStr.substring(0, 4)),
      parseInt(checkInStr.substring(4, 6)) - 1,
      parseInt(checkInStr.substring(6, 8))
    )
    const checkOutDate = new Date(
      parseInt(checkOutStr.substring(0, 4)),
      parseInt(checkOutStr.substring(4, 6)) - 1,
      parseInt(checkOutStr.substring(6, 8))
    )

    const diffTime = checkOutDate.getTime() - checkInDate.getTime()
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

    return diffDays > 0 ? diffDays : 1
  } catch (error) {
    console.warn('Error calculating nights:', error)
    return 1
  }
}

const extractAdultsCount = (orderData: any): number => {
  // Try to extract from booker or default to 1
  return orderData?.adultsCount || 1
}

const extractChildrenCount = (orderData: any): number => {
  // Try to extract from booker or default to 0
  return orderData?.childrenCount || 0
}

const formatDateFromInt = (dateInt: number | string): string => {
  if (!dateInt) return ''

  try {
    const dateStr = String(dateInt)
    if (dateStr.length === 8) {
      // YYYYMMDD format
      const year = dateStr.substring(0, 4)
      const month = dateStr.substring(4, 6)
      const day = dateStr.substring(6, 8)
      return `${year}-${month}-${day}`
    }
  } catch (error) {
    console.warn('Error formatting date:', error)
  }

  return String(dateInt)
}

const getBookerName = (booker: any): string => {
  if (!booker) return ''

  const firstName = booker.firstName || ''
  const lastName = booker.lastName || ''

  return `${firstName} ${lastName}`.trim()
}

const loadOrders = async () => {
  loading.value = true
  try {
    // Convert search form to backend format
    const requestParams: any = {}

    if (searchForm.orderId) {
      requestParams.orderIds = [searchForm.orderId]
    }

    if (searchForm.hotelName || searchForm.guestName) {
      requestParams.keyword = searchForm.hotelName || searchForm.guestName
    }

    if (dateRange.value) {
      requestParams.createTimeWindow = {
        start: dateRange.value[0],
        end: dateRange.value[1]
      }
    }

    const response = await orderApi.getTenantOrderList(requestParams)

    // Handle BFF Table response
    if (response.rows) {
      // Convert BFF Table data using real backend data
      orders.value = response.rows.map(row => {
        const orderData = row.raw

        // Extract hotel information from rooms array if available
        const hotelInfo = orderData?.rooms?.[0] || {}
        const roomInfo = orderData?.rooms?.[0] || {}

        // Calculate nights from check-in/out dates
        const nights = calculateNights(orderData?.checkIn, orderData?.checkOut)

        // Extract guest counts from booker info or default values
        const adultsCount = extractAdultsCount(orderData)
        const childrenCount = extractChildrenCount(orderData)
        const roomsCount = orderData?.rooms?.length || 1

        return {
          orderId: orderData?.id || row.key,
          confirmationNumber: orderData?.confirmNumber || '',
          status: mapOrderStatus(orderData?.status || 0),
          totalPrice: (orderData?.orderAccount?.salesAmount || 0) / 100, // Convert from cents
          currency: orderData?.orderAccount?.salesCurrency || 'USD',
          hotel: {
            id: hotelInfo?.hotelId || '',
            name: hotelInfo?.hotelName || 'Unknown Hotel',
            address: hotelInfo?.hotelAddress || '',
            city: hotelInfo?.hotelCity || '',
            country: hotelInfo?.hotelCountry || '',
            rating: hotelInfo?.rating || 0
          },
          room: {
            id: roomInfo?.roomId || '',
            name: roomInfo?.roomName || 'Standard Room',
            type: roomInfo?.roomType || ''
          },
          checkIn: formatDateFromInt(orderData?.checkIn) || '',
          checkOut: formatDateFromInt(orderData?.checkOut) || '',
          nights: nights,
          adults: adultsCount,
          children: childrenCount,
          rooms: roomsCount,
          guests: [], // Will be populated from detailed order data if needed
          createdAt: orderData?.createTime || '',
          updatedAt: orderData?.updateTime || '',
          bookedBy: {
            id: orderData?.booker?.id || '',
            name: getBookerName(orderData?.booker) || '',
            email: orderData?.booker?.email || ''
          }
        }
      })
    } else {
      orders.value = []
    }

    total.value = response.total || 0
  } catch (error) {
    console.error('Failed to load orders:', error)
    ElMessage.error(getStarlingText('booking.order.loadFailed', t('booking.order.loadFailed')))
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  currentPage.value = 1
  loadOrders()
}

const resetSearch = () => {
  Object.assign(searchForm, {
    orderId: '',
    hotelName: '',
    guestName: '',
    status: undefined
  })
  dateRange.value = null
  currentPage.value = 1
  loadOrders()
}

const refreshOrders = () => {
  loadOrders()
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  loadOrders()
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  loadOrders()
}

const handleSelectionChange = (selection: Order[]) => {
  selectedOrders.value = selection
}

const viewOrderDetail = (orderId: string) => {
  router.push(`/booking/orders/detail/${orderId}`)
}

const canCancelOrder = (status: string) => {
  // Can cancel if status is created, paid, pending, or confirmed
  return ['created', 'paid', 'pending', 'confirmed'].includes(status)
}

const cancelOrder = async (orderId: string) => {
  // 找到对应的订单信息
  const order = orders.value.find(o => o.orderId === orderId)
  if (!order) {
    ElMessage.error('订单信息不存在')
    return
  }

  // 准备取消对话框的订单信息
  cancelOrderInfo.value = {
    orderId: order.orderId,
    hotelName: order.hotelName || '未知酒店',
    checkIn: order.checkIn || '',
    checkOut: order.checkOut || '',
    amount: formatCurrency(order.totalAmount)
  }

  cancelDialogVisible.value = true
}

const handleCancelSuccess = () => {
  ElMessage.success('订单取消成功')
  refreshOrders()
}

const exportOrders = async () => {
  exporting.value = true
  try {
    const blob = await orderApi.exportOrders(searchParams.value)
    
    // Create download link
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `orders-${new Date().toISOString().split('T')[0]}.xlsx`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
    
    ElMessage.success('订单导出成功')
  } catch (error) {
    console.error('Export orders failed:', error)
    ElMessage.error('导出订单失败')
  } finally {
    exporting.value = false
  }
}

const batchExport = () => {
  // Implement batch export logic
  ElMessage.info('批量导出功能开发中')
}

const batchCancel = async () => {
  if (selectedOrders.value.length === 0) {
    ElMessage.warning('请先选择要取消的订单')
    return
  }

  // 检查选中的订单是否都可以取消
  const cancellableOrders = selectedOrders.value.filter(order => canCancelOrder(order.status))
  if (cancellableOrders.length === 0) {
    ElMessage.warning('选中的订单都无法取消')
    return
  }

  if (cancellableOrders.length < selectedOrders.value.length) {
    ElMessage.warning(`只有 ${cancellableOrders.length} 个订单可以取消，其他订单状态不允许取消`)
  }

  try {
    await ElMessageBox.confirm(
      `确定要取消选中的 ${cancellableOrders.length} 个订单吗？此操作不可撤销。`,
      '批量取消订单',
      {
        confirmButtonText: '确定取消',
        cancelButtonText: '取消',
        type: 'warning',
        dangerouslyUseHTMLString: true
      }
    )

    // 显示进度
    const loadingInstance = ElLoading.service({
      lock: true,
      text: '正在取消订单...',
      background: 'rgba(0, 0, 0, 0.7)'
    })

    try {
      // 批量取消订单
      const cancelPromises = cancellableOrders.map(order =>
        bookingApi.cancelBooking({
          orderId: order.orderId,
          reason: '批量取消操作'
        })
      )

      const results = await Promise.allSettled(cancelPromises)

      // 统计结果
      const successCount = results.filter(result => result.status === 'fulfilled').length
      const failureCount = results.length - successCount

      if (successCount > 0) {
        ElMessage.success(`成功取消 ${successCount} 个订单`)
      }

      if (failureCount > 0) {
        ElMessage.warning(`${failureCount} 个订单取消失败`)
      }

      // 刷新订单列表
      refreshOrders()

      // 清空选择
      selectedOrders.value = []

    } finally {
      loadingInstance.close()
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Batch cancel failed:', error)
      ElMessage.error('批量取消失败')
    }
  }
}

// Helper methods
const formatCurrency = (amount: any) => {
  if (!amount) return '¥0.00'
  if (typeof amount === 'number') {
    return `¥${(amount / 100).toFixed(2)}`
  }
  return amount.toString()
}

// Initialize
onMounted(() => {
  // Check if there's an orderId or platformOrderId in query params and redirect to detail page
  const orderId = route.query.orderId as string
  const platformOrderId = route.query.platformOrderId as string

  if (platformOrderId || orderId) {
    // Clear the query parameters and redirect to order detail
    router.replace({ path: route.path })

    // Prefer platformOrderId if available, fallback to orderId
    if (platformOrderId) {
      // For platformOrderId, we need to convert it to a number and use the new API format
      const platformOrderIdNum = parseInt(platformOrderId, 10)
      if (!isNaN(platformOrderIdNum)) {
        // Show order detail using platformOrderId
        showOrderDetailByPlatformOrderId(platformOrderIdNum)
      } else {
        ElMessage.error('无效的平台订单ID')
      }
    } else {
      // Fallback to legacy orderId
      viewOrderDetail(orderId)
    }
    return
  }

  loadOrders()
})

// Helper method to show order detail using platformOrderId
const showOrderDetailByPlatformOrderId = async (platformOrderId: number) => {
  try {
    // First, try to get order detail using platformOrderId
    const orderDetail = await orderApi.getTenantOrderDetail({ platformOrderId })
    if (orderDetail.summary?.id) {
      // Use the order ID from the response to show the detail dialog
      selectedOrderId.value = orderDetail.summary.id
      detailDialogVisible.value = true
    } else {
      ElMessage.error('未找到对应的订单详情')
    }
  } catch (error) {
    console.error('Failed to load order detail by platformOrderId:', error)
    ElMessage.error('加载订单详情失败')
  }
}
</script>

<style scoped lang="scss">
.order-management {
  padding: 24px;
  
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    
    .page-title {
      font-size: 28px;
      font-weight: 600;
      color: #303133;
      margin: 0;
    }
    
    .header-actions {
      display: flex;
      gap: 12px;
    }
  }
  
  .filter-card {
    margin-bottom: 16px;
  }
  
  .table-card {
    .price-cell {
      .currency {
        font-size: 12px;
        color: #909399;
        margin-right: 4px;
      }
      
      .amount {
        font-weight: 600;
        color: #E6A23C;
      }
    }
    
    .text-secondary {
      font-size: 12px;
      color: #909399;
    }
    
    .pagination-wrapper {
      display: flex;
      justify-content: center;
      margin-top: 24px;
    }
  }
  
  .batch-actions {
    position: fixed;
    bottom: 24px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1000;
    
    .batch-content {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 12px 24px;
      
      .batch-buttons {
        display: flex;
        gap: 12px;
        margin-left: 24px;
      }
    }
  }
}
</style>
