<template>
  <el-dialog
    v-model="dialogVisible"
    title="取消订单"
    width="500px"
    :before-close="handleClose"
  >
    <div class="cancel-order-form">
      <div class="order-info">
        <h4>订单信息</h4>
        <div class="info-item">
          <span class="label">订单号:</span>
          <span class="value">{{ orderInfo.orderId }}</span>
        </div>
        <div class="info-item">
          <span class="label">酒店名称:</span>
          <span class="value">{{ orderInfo.hotelName }}</span>
        </div>
        <div class="info-item">
          <span class="label">入住日期:</span>
          <span class="value">{{ orderInfo.checkIn }} - {{ orderInfo.checkOut }}</span>
        </div>
        <div class="info-item">
          <span class="label">订单金额:</span>
          <span class="value">{{ orderInfo.amount }}</span>
        </div>
      </div>

      <el-divider />

      <el-form ref="formRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="取消原因" prop="reason" required>
          <el-select
            v-model="form.reason"
            placeholder="请选择取消原因"
            style="width: 100%"
            @change="handleReasonChange"
          >
            <el-option
              v-for="reason in cancelReasons"
              :key="reason.value"
              :label="reason.label"
              :value="reason.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item
          v-if="form.reason === 'other'"
          label="详细说明"
          prop="customReason"
          required
        >
          <el-input
            v-model="form.customReason"
            type="textarea"
            :rows="3"
            placeholder="请详细说明取消原因"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="退款方式" prop="refundMethod">
          <el-radio-group v-model="form.refundMethod">
            <el-radio value="original">原路退回</el-radio>
            <el-radio value="manual">人工处理</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item>
          <el-checkbox v-model="form.notifyCustomer">
            发送取消通知给客户
          </el-checkbox>
        </el-form-item>
      </el-form>

      <div class="warning-notice">
        <el-alert
          title="取消须知"
          type="warning"
          :closable="false"
          show-icon
        >
          <template #default>
            <ul>
              <li>订单取消后无法恢复，请谨慎操作</li>
              <li>根据酒店政策，可能产生取消费用</li>
              <li>退款将在3-7个工作日内到账</li>
              <li>如有疑问，请联系客服</li>
            </ul>
          </template>
        </el-alert>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="danger"
          :loading="submitting"
          @click="handleConfirm"
        >
          确认取消订单
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { bookingApi } from '@/api/booking/bookingApi'

interface OrderInfo {
  orderId: string
  hotelName: string
  checkIn: string
  checkOut: string
  amount: string
}

interface Props {
  visible: boolean
  orderInfo: OrderInfo
}

interface Emits {
  (e: 'update:visible', visible: boolean): void
  (e: 'success'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 响应式数据
const formRef = ref<FormInstance>()
const submitting = ref(false)

const form = reactive({
  reason: '',
  customReason: '',
  refundMethod: 'original',
  notifyCustomer: true
})

// 取消原因选项
const cancelReasons = [
  { label: '客户主动取消', value: 'customer_request' },
  { label: '酒店无房', value: 'hotel_unavailable' },
  { label: '价格变动', value: 'price_change' },
  { label: '行程变更', value: 'itinerary_change' },
  { label: '重复预订', value: 'duplicate_booking' },
  { label: '支付问题', value: 'payment_issue' },
  { label: '系统错误', value: 'system_error' },
  { label: '其他原因', value: 'other' }
]

// 表单验证规则
const rules: FormRules = {
  reason: [
    { required: true, message: '请选择取消原因', trigger: 'change' }
  ],
  customReason: [
    { required: true, message: '请填写详细说明', trigger: 'blur' },
    { min: 5, max: 200, message: '说明长度在 5 到 200 个字符', trigger: 'blur' }
  ]
}

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 监听器
watch(() => props.visible, (newVal) => {
  if (newVal) {
    resetForm()
  }
})

// 方法
const handleReasonChange = () => {
  if (form.reason !== 'other') {
    form.customReason = ''
  }
}

const resetForm = () => {
  form.reason = ''
  form.customReason = ''
  form.refundMethod = 'original'
  form.notifyCustomer = true
  formRef.value?.clearValidate()
}

const handleClose = () => {
  dialogVisible.value = false
}

const handleConfirm = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    
    await ElMessageBox.confirm(
      '确定要取消这个订单吗？此操作不可撤销。',
      '确认取消',
      {
        confirmButtonText: '确定取消',
        cancelButtonText: '再想想',
        type: 'warning',
        dangerouslyUseHTMLString: true
      }
    )

    submitting.value = true

    const cancelReason = form.reason === 'other' ? form.customReason : 
      cancelReasons.find(r => r.value === form.reason)?.label || form.reason

    await bookingApi.cancelBooking({
      orderId: props.orderInfo.orderId,
      reason: cancelReason,
      refundMethod: form.refundMethod,
      notifyCustomer: form.notifyCustomer
    })

    ElMessage.success('订单取消成功')
    emit('success')
    handleClose()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Cancel order failed:', error)
      ElMessage.error('取消订单失败，请重试')
    }
  } finally {
    submitting.value = false
  }
}
</script>

<style scoped>
.cancel-order-form {
  padding: 0 8px;
}

.order-info {
  background: #f8f9fa;
  padding: 16px;
  border-radius: 6px;
  margin-bottom: 16px;
}

.order-info h4 {
  margin: 0 0 12px 0;
  color: #303133;
  font-size: 14px;
  font-weight: 600;
}

.info-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 13px;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-item .label {
  color: #606266;
}

.info-item .value {
  color: #303133;
  font-weight: 500;
}

.warning-notice {
  margin-top: 16px;
}

.warning-notice ul {
  margin: 0;
  padding-left: 16px;
}

.warning-notice li {
  margin-bottom: 4px;
  font-size: 12px;
  color: #e6a23c;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
